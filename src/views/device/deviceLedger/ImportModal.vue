<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量导入"
    :width="800"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <FaImportHeaderStep v-model:current="activeStep"/>

    <div class="import-main" v-show="activeStep == 0">
      <FaUploadCard v-model:value="fileId"/>
      <FaDownloadCard download-url="/file/设备台账导入模板.xlsx"/>

      <a-alert class="mt-4">
        <template #message>
          <ol>
            <li>1. 导入从第4行开始，请注意；</li>
            <li>2. 所有标*的字段为必填项；</li>
            <li>3. 设备状态可选值：在用、维修中、报废；</li>
            <li>4. 购置日期格式：YYYY-MM-DD。</li>
          </ol>
        </template>
      </a-alert>
    </div>

    <div class="import-main" v-show="activeStep == 1">
      <a-table :data-source="list" :columns="columns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '440px' }" class="import-preview-table">
        <template #bodyCell="{ column, record, index }">
          <template v-for="item in tableData">
            <template v-if="column.key === item.dataIndex">
              <a-input v-model:value="record[column.key]" style="background: #FFF" />
            </template>
          </template>
          <template v-if="column.key === 'action'">
            <a-button class="action-btn" type="link" color="error" @click="handleDelItem(index)" size="small">删除</a-button>
          </template>
        </template>
      </a-table>
    </div>

    <div class="import-main" v-show="activeStep == 2">
      <FaImportSuccessCard v-if="!result.resultType" :success-num="result.snum"/>
      <FaImportFailCard v-if="result.resultType" :success-num="result.snum" :fail-num="result.fnum">
        <a-table :data-source="resultList" :columns="resultColumns" size="small" :pagination="false" :scroll="{ x: 'max-content', y: '205px' }"/>
      </FaImportFailCard>
    </div>

    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1">{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleNext" :loading="btnLoading" v-if="activeStep < 2" :disabled="activeStep === 0 && !fileId">{{ t('common.next') }}</a-button>
      <a-button type="primary" @click="handleClose(true)" v-else>关闭</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import { reactive, ref, toRefs, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { deviceLedgerApi } from '/@/api';
import FaDownloadCard from "/@/components/Fa/Import/src/FaDownloadCard.vue";
import FaUploadCard from "/@/components/Fa/Import/src/FaUploadCard.vue";
import FaImportHeaderStep from "/@/components/Fa/Import/src/FaImportHeaderStep.vue";
import FaImportSuccessCard from "/@/components/Fa/Import/src/FaImportSuccessCard.vue";
import FaImportFailCard from "/@/components/Fa/Import/src/FaImportFailCard.vue";

interface State {
  activeStep: number;
  fileId?: string;
  btnLoading: boolean;
  list: any[];
  result: any;
  resultList: any[];
}

const emit = defineEmits(['register', 'reload']);
const [registerModal, { closeModal, getVisible }] = useModalInner(init);
const { createMessage } = useMessage();
const { t } = useI18n();

// 定义表格字段
const tableData = [
  { title: '设备名称*', dataIndex: 'name', key: 'name' },
  { title: '设备编号*', dataIndex: 'code', key: 'code' },
  { title: '设备类别*', dataIndex: 'categoryName', key: 'categoryName' },
  { title: '设备状态*', dataIndex: 'statusName', key: 'statusName' },
  { title: '责任人', dataIndex: 'responsiblePersonName', key: 'responsiblePersonName' },
  { title: '所在位置', dataIndex: 'location', key: 'location' },
  { title: '型号规格', dataIndex: 'model', key: 'model' },
  { title: '生产厂商', dataIndex: 'manufacturer', key: 'manufacturer' }
];

// 表格列定义
const columns: any[] = [
  ...tableData,
  { title: '操作', dataIndex: 'action', key: 'action', width: 50, fixed: 'right' },
];

const resultColumns: any[] = [
  ...tableData
];

const state = reactive<State>({
  activeStep: 0,
  fileId: undefined,
  btnLoading: false,
  list: [],
  result: {},
  resultList: [],
});

const { activeStep, fileId, btnLoading, list, result, resultList } = toRefs(state);

// 添加一个监听，确保每次模态框打开时activeStep都是0
watch(getVisible, (visible) => {
  if (visible) {
    state.activeStep = 0;
    state.fileId = undefined;
    state.btnLoading = false;
    state.list = [];
    state.result = {};
    state.resultList = [];
  }
}, { immediate: true });

function init() {
  state.activeStep = 0;
  state.fileId = undefined;
  state.btnLoading = false;
  state.list = [];
  state.result = {};
  state.resultList = [];
}

// 上一步
function handlePrev() {
  if (state.activeStep == 0) return;
  state.activeStep -= 1;
}

// 下一步
function handleNext() {
  if (state.activeStep == 0) {
    if (!state.fileId) return createMessage.warning('请先上传文件');
    state.btnLoading = true;
    deviceLedgerApi.importPreview({
      fileId: state.fileId
    })
      .then(res => {
        state.list = res.data || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
    return;
  }
  if (state.activeStep == 1) {
    if (!state.list.length) return createMessage.warning('导入数据为空');
    state.btnLoading = true;
    deviceLedgerApi.importData({ list: state.list })
      .then(res => {
        state.result = res.data;
        state.resultList = res.data.failResult || [];
        state.btnLoading = false;
        state.activeStep += 1;
      })
      .catch(() => {
        state.btnLoading = false;
      });
  }
}

// 删除一条记录
function handleDelItem(index) {
  state.list.splice(index, 1);
}

// 关闭并可选刷新
function handleClose(reload = false) {
  // 确保关闭前重置状态
  state.activeStep = 0;
  closeModal();
  if (reload) emit('reload');
}
</script>

<style scoped>
.mt-4 {
  margin-top: 16px;
}
</style> 
