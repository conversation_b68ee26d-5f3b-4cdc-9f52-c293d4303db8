<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="modalTitle" showOkBtn @ok="handleSubmit" :destroyOnClose="true" width="900px">
    <div class="inspection-form">
      <a-form :colon="false" :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef">
        <a-row>
          <a-col :span="12">
            <a-form-item label="设备" name="deviceId" :required="true">
              <jnpf-select v-model:value="dataForm.deviceId" :options="deviceOptions" showSearch placeholder="请选择设备" @change="handleDeviceChange" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="点检时间" name="inspectionTime" :required="true">
              <jnpf-date-picker v-model:value="dataForm.inspectionTime" :format="'YYYY-MM-DD HH:mm:ss'" :showTime="true" allowClear />
            </a-form-item>
          </a-col>

          <a-col :span="12" v-if="!id">
            <a-form-item label="点检模板" name="templateId">
              <jnpf-select
                v-model:value="dataForm.templateId"
                :options="templateOptions"
                showSearch
                placeholder="请选择点检模板"
                @change="handleTemplateChange" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="点检状态" name="inspectionStatus" :required="true">
              <a-radio-group v-model:value="dataForm.inspectionStatus" @change="handleStatusChange">
                <a-radio value="1">正常</a-radio>
                <a-radio value="2">异常</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="点检人" name="inspector" :required="true">
              <jnpf-user-select v-model:value="dataForm.inspector" placeholder="请选择点检人员" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="联系电话" name="contactPhone">
              <a-input v-model:value="dataForm.contactPhone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="当前位置" name="location">
              <a-input v-model:value="dataForm.location"  />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="dataForm.remark" placeholder="请输入备注信息" :auto-size="{ minRows: 2, maxRows: 4 }" />
            </a-form-item>
          </a-col>

          <a-col :span="24">
            <a-form-item label="现场图片" name="attachmentId">
              <JnpfFaUploadFileQiniu v-model:value="dataForm.attachmentId" prefix="inspection" :multiple="true" :maxCount="5" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template v-if="showExceptionForm">
        <div class="section-title">问题反馈</div>

        <a-form layout="vertical">
          <a-form-item label="问题描述" required>
            <a-textarea v-model:value="exceptionForm.description" placeholder="请详细描述设备问题" :rows="3" />
          </a-form-item>
        </a-form>
      </template>
      <template v-if="showInspectionItems">
        <div class="section-title">点检项目</div>

        <div class="inspection-actions">
          <a-button class="fa-mr12" type="primary" ghost @click="handleCheckAllNormal" :disabled="inspectionItems.length === 0">
            <check-outlined />
            全部正常
          </a-button>

          <a-button type="primary" @click="handleAddCategory">
            <plus-outlined />
            新增类别
          </a-button>
        </div>

        <a-collapse v-if="inspectionItems.length > 0" v-model:activeKey="activeCollapseKeys">
          <a-collapse-panel v-for="(category, index) in inspectionItems" :key="category.id || index">
            <template #header>
              <div class="category-header">
                <a-input
                  v-if="category.isEditing"
                  v-model:value="category.name"
                  placeholder="类别名称"
                  @pressEnter="category.isEditing = false"
                  @blur="category.isEditing = false"
                  size="small"
                  style="width: 200px" />
                <span v-else @click.stop="handleEditCategoryName(category)">{{ category.name }}</span>
              </div>
            </template>
            <div class="category-actions">
              <a-button type="link" @click="handleAddItem(category)">
                <plus-outlined />
                新增点检项
              </a-button>
              <a-button type="link" danger @click="handleDeleteCategory(index)">
                <delete-outlined />
                删除类别
              </a-button>
            </div>

            <div class="inspection-item-list">
              <div v-for="(item, itemIndex) in category.items" :key="itemIndex" class="inspection-item">
                <div class="inspection-item-header">
                  <div class="inspection-item-name">
                    <a-input
                      v-if="item.isEditing"
                      v-model:value="item.name"
                      placeholder="点检项名称"
                      @pressEnter="item.isEditing = false"
                      @blur="item.isEditing = false" />
                    <span v-else @click="item.isEditing = true">{{ item.name }}</span>
                  </div>
                  <div class="inspection-item-actions">
                    <div class="inspection-item-status">
                      <a-radio-group v-model:value="item.status">
                        <a-radio value="1">正常</a-radio>
                        <a-radio value="2">异常</a-radio>
                      </a-radio-group>
                    </div>
                    <a-button type="link" danger @click="handleDeleteItem(category, itemIndex)">
                      <delete-outlined />
                    </a-button>
                  </div>
                </div>

                <div v-if="item.status === '2'" class="inspection-item-exception">
                  <a-textarea v-model:value="item.exceptionDesc" placeholder="请输入异常描述" :rows="2" />
                </div>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>

        <a-empty v-else description='暂无点检项，请点击"新增类别"按钮添加' style="margin-top: 20px" />
      </template>
    </div>
  </BasicPopup>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, toRefs, onMounted } from 'vue';
  import { BasicPopup, usePopupInner } from '/@/components/Popup';
  import {
    deviceInspectionApi,
    deviceInspectionItemApi,
    deviceInspectionItemTemplateApi,
    deviceLedgerApi,
    deviceInspectionTemplateApi
  } from '/@/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CheckOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { useBaseStore } from '/@/store/modules/base';
  import type { FormInstance } from 'ant-design-vue';
  import { Rule } from '/@/components/Form';
  import JnpfSelect from '/@/components/Jnpf/Select/src/Select.vue';
  import JnpfUserSelect from '/@/components/Jnpf/Organize/src/UserSelect.vue';
  import JnpfDatePicker from '/@/components/Jnpf/DatePicker/src/DatePicker.vue';
  import JnpfFaUploadFileQiniu from '/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue';
  import dayjs from 'dayjs';

  const { createMessage, createConfirm } = useMessage();
  const baseStore = useBaseStore();

  // 表单数据
  const id = ref<string>('');
  const modalTitle = computed(() => (id.value ? '编辑点检记录' : '新增点检记录'));
  const formRef = ref<FormInstance>();

  interface State {
    dataForm: any;
    dataRule: Record<string, Rule[]>;
  }

  const state = reactive<State>({
    dataForm: {
      deviceId: '',
      deviceName: '',
      deviceCode: '',
      templateId: '',
      inspectionTime: '',
      inspectionStatus: '1',
      inspector: '',
      contactPhone: '',
      location: '',
      remark: '',
      attachmentId: [],
    },
    dataRule: {
      deviceId: [{ required: true, message: '设备不能为空', trigger: 'change' }],
      inspectionTime: [{ required: true, message: '点检时间不能为空', trigger: 'change' }],
      inspectionStatus: [{ required: true, message: '点检状态不能为空', trigger: 'change' }],
      inspector: [{ required: true, message: '点检人不能为空', trigger: 'change' }],
    },
  });
  const { dataForm, dataRule } = toRefs(state);

  // 设备状态选项
  const deviceStatusOptions = ref<any[]>([]);
  // 设备选项
  const deviceOptions = ref<any[]>([]);
  // 新增：模板选项
  const templateOptions = ref<any[]>([]);

  // 点检项列表
  const inspectionItems = ref<any[]>([]);
  const activeCollapseKeys = ref<string[]>([]);
  const showInspectionItems = ref<boolean>(true);

  // 异常表单数据
  const exceptionForm = ref({
    description: '',
    fileList: [],
  });
  const showExceptionForm = ref<boolean>(false);

  // 定义emit事件
  const emit = defineEmits(['reload']);

  // 注册弹窗
  const [registerPopup, { setPopupProps, closePopup }] = usePopupInner(async (data: any) => {
    setPopupProps({ confirmLoading: false, loading: true });

    // 重置数据
    resetForm();
    inspectionItems.value = [];
    exceptionForm.value = { description: '', fileList: [] };
    showExceptionForm.value = false;

    // 初始化数据字典
    await initDictionaries();

    // 设置ID
    id.value = data?.id || '';

    // 并行获取数据，提高加载速度
    await Promise.all([fetchDeviceList(), fetchTemplateList(), id.value && data?.record ? loadDetail(data.record) : Promise.resolve()]);

    if (!id.value) {
      // 新增模式，设置默认值
      dataForm.value.inspectionTime = dayjs();
      dataForm.value.inspectionStatus = '1';
      dataForm.value.attachmentId = []; // 确保现场图片初始化为空数组

      // 获取当前位置
      // getCurrentLocation();
    }

    setPopupProps({ loading: false });
  });

  // 初始化数据字典
  async function initDictionaries() {
    try {
      const statusData = await baseStore.getFormattedDictionaryData('deviceStatus');
      deviceStatusOptions.value = Array.isArray(statusData) ? statusData : [];
    } catch (error) {
      console.error('初始化数据字典失败', error);
    }
  }

  // 获取设备列表
  async function fetchDeviceList() {
    try {
      const { data } = await deviceLedgerApi.list({});
      if (data && Array.isArray(data)) {
        deviceOptions.value = data.map(item => ({
          id: item.id,
          fullName: `${item.name}(${item.code || ''})`,
          code: item.code || '',
          name: item.name,
        }));
      }
    } catch (error) {
      console.error('获取设备列表失败', error);
    }
  }

  // 新增：获取模板列表
  async function fetchTemplateList() {
    try {
      const { data } = await deviceInspectionTemplateApi.list({});
      if (data && Array.isArray(data)) {
        templateOptions.value = data.map(item => ({
          id: item.id,
          fullName: item.templateName,
          deviceType: item.deviceType,
        }));
      }
    } catch (error) {
      console.error('获取模板列表失败', error);
    }
  }

  // 加载详情
  async function loadDetail(record: any) {
    try {
      if (record) {
        const { data } = await deviceInspectionApi.getById(id.value);
        if (data) {
          // 设置表单值
          Object.keys(dataForm.value).forEach(key => {
            if (key in data) {
              // 日期类型特殊处理
              if (key === 'inspectionTime' && data[key]) {
                dataForm.value[key] = dayjs(data[key]);
              } else {
                dataForm.value[key] = data[key];
              }
            }
          });

          // 处理图片
          if (data.attachmentId && Array.isArray(data.attachmentId)) {
            dataForm.value.attachmentId = data.attachmentId;
          } else if (data.attachmentId && typeof data.attachmentId === 'string') {
            dataForm.value.attachmentId = data.attachmentId.split(',');
          }

          // 如果是异常状态，显示异常表单
          showExceptionForm.value = data.inspectionStatus === '2';
          if (showExceptionForm.value && data.exceptionDesc) {
            exceptionForm.value.description = data.exceptionDesc;
          }

          // 处理异常图片
          if (data.exceptionImages && Array.isArray(data.exceptionImages)) {
            exceptionForm.value.fileList = data.exceptionImages;
          } else if (data.exceptionImages && typeof data.exceptionImages === 'string') {
            exceptionForm.value.fileList = data.exceptionImages.split(',');
          }

          // 加载点检项记录（编辑模式下，通过点检记录ID加载已有的点检项）
          if (data.id) {
            await loadInspectionItemsByRecordId(data.id);
          }
        }
      }
    } catch (error) {
      console.error('加载详情失败', error);
      createMessage.error('加载详情失败');
    }
  }

  // 通过点检记录ID加载点检项
  async function loadInspectionItemsByRecordId(inspectionId: string) {
    try {
      const res = await deviceInspectionItemApi.list({ inspectionId: inspectionId });
      const data = res.data;

      if (data && Array.isArray(data)) {
        // 按类别分组
        const groupedItems = {};
        data.forEach(item => {
          if (!groupedItems[item.categoryId]) {
            groupedItems[item.categoryId] = {
              id: item.categoryId,
              name: item.categoryName,
              items: [],
              isEditing: false, // 添加类别编辑标志
            };
          }

          // 确保每个检查项都有状态值
          if (!item.status) {
            item.status = '1'; // 默认正常
          }

          // 添加编辑标志
          item.isEditing = false;

          groupedItems[item.categoryId].items.push(item);
        });

        inspectionItems.value = Object.values(groupedItems);

        // 默认展开第一个分类
        if (inspectionItems.value.length > 0) {
          activeCollapseKeys.value = [inspectionItems.value[0].id];
        }
      }
    } catch (error) {
      console.error('加载点检项失败', error);
      createMessage.error('加载点检项失败');
    }
  }

  // 设备变更处理
  async function handleDeviceChange(deviceId: string) {
    if (deviceId) {
      const selectedDevice = deviceOptions.value.find(item => item.id === deviceId);
      if (selectedDevice) {
        // 设置设备名称和编号
        dataForm.value.deviceName = selectedDevice.name;
        dataForm.value.deviceCode = selectedDevice.code || '';
      }
    } else {
      // 清空设备相关信息
      dataForm.value.deviceName = '';
      dataForm.value.deviceCode = '';
    }
  }

  // 新增：模板变更处理
  async function handleTemplateChange(templateId: string) {
    if (templateId) {
      // 加载模板点检项
      await loadTemplateItems(templateId);
    } else {
      // 取消选择模板，清空点检项
      inspectionItems.value = [];
    }
  }

  // 新增：加载模板点检项
  async function loadTemplateItems(templateId: string) {
    try {
      const res = await deviceInspectionItemTemplateApi.list({ templateId: templateId });
      const data = res.data;

      if (data && Array.isArray(data)) {
        // 按类别分组
        const groupedItems = {};
        data.forEach(item => {
          if (!groupedItems[item.categoryId]) {
            groupedItems[item.categoryId] = {
              id: item.categoryId,
              name: item.categoryName,
              items: [],
              isEditing: false, // 添加类别编辑标志
            };
          }

          // 确保每个检查项都有状态值
          if (!item.status) {
            item.status = '1'; // 默认正常
          }

          // 添加编辑标志和临时ID（不使用模板原有的ID）
          const tempItem = {
            ...item,
            id: undefined, // 不使用模板项的ID，让后端生成新ID
            templateItemId: item.id, // 保存模板项的ID，以便需要时参考
            isEditing: false,
          };

          groupedItems[item.categoryId].items.push(tempItem);
        });

        inspectionItems.value = Object.values(groupedItems);

        // 默认展开第一个分类
        if (inspectionItems.value.length > 0) {
          activeCollapseKeys.value = [inspectionItems.value[0].id];
        }
      }
    } catch (error) {
      console.error('加载模板点检项失败', error);
      createMessage.error('加载模板点检项失败');
    }
  }

  // 状态变更处理
  function handleStatusChange(e) {
    showExceptionForm.value = e.target.value === '2';
  }

  // 获取当前位置
  function getCurrentLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const latitude = position.coords.latitude;
          const longitude = position.coords.longitude;

          // 这里可以调用地图API将经纬度转换为地址
          // 简化处理，直接显示经纬度
          dataForm.value.location = `经度: ${longitude.toFixed(6)}, 纬度: ${latitude.toFixed(6)}`;
        },
        error => {
          console.error('获取位置失败', error);
          dataForm.value.location = '位置获取失败';
        },
      );
    } else {
      dataForm.value.location = '浏览器不支持获取位置';
    }
  }

  // 全部标记为正常
  function handleCheckAllNormal() {
    inspectionItems.value.forEach(category => {
      category.items.forEach(item => {
        item.status = '1';
        item.exceptionDesc = '';
      });
    });
  }

  // 重置表单
  function resetForm() {
    dataForm.value = {
      deviceId: '',
      deviceName: '',
      deviceCode: '',
      templateId: '',
      inspectionTime: '',
      inspectionStatus: '1',
      inspector: '',
      contactPhone: '',
      location: '',
      remark: '',
      attachmentId: [],
    };

    // 重置表单校验
    formRef.value?.resetFields();
  }

  // 提交表单
  async function handleSubmit() {
    if (!formRef.value) return;

    try {
      // 1. 表单验证
      await formRef.value.validate();

      // 2. 异常状态验证
      if (dataForm.value.inspectionStatus === '2') {
        if (!exceptionForm.value.description) {
          createMessage.error('请填写问题描述');
          return false;
        }
      }

      // 2.1 验证每个类别至少有一个点检项
      if (inspectionItems.value.length > 0) {
        const emptyCategories = inspectionItems.value.filter(category => !category.items || category.items.length === 0);
        if (emptyCategories.length > 0) {
          // 找出第一个空类别的名称用于提示
          const firstEmptyCategory = emptyCategories[0];
          createMessage.error(`类别"${firstEmptyCategory.name}"下至少需要一个点检项`);
          return false;
        }
      }

      // 3. 点检项异常状态验证
      let hasInvalidItem = false;
      if (inspectionItems.value.length > 0) {
        for (const category of inspectionItems.value) {
          for (const item of category.items) {
            if (item.status === '2' && !item.exceptionDesc) {
              hasInvalidItem = true;
              createMessage.error(`点检项"${item.name}"异常但未填写异常描述`);
              break;
            }
          }
          if (hasInvalidItem) break;
        }

        if (hasInvalidItem) return false;
      }

      setPopupProps({ confirmLoading: true });

      try {
        // 4. 准备表单数据
        const formData = { ...dataForm.value };

        // 处理日期类型数据
        if (formData.inspectionTime && dayjs.isDayjs(formData.inspectionTime)) {
          formData.inspectionTime = formData.inspectionTime.format('YYYY-MM-DD HH:mm:ss');
        }

        // 如果有ID，添加到提交数据
        if (id.value) {
          formData.id = id.value;
        }

        // 删除templateId，不需要提交到后端
        delete formData.templateId;

        // 处理图片上传 - 确保attachmentId始终为字符串格式，即使是空数组
        if (Array.isArray(formData.attachmentId)) {
          formData.attachmentId = formData.attachmentId.join(',');
        } else {
          formData.attachmentId = '';
        }

        // 如果是异常状态，添加异常描述
        if (formData.inspectionStatus === '2') {
          formData.exceptionDesc = exceptionForm.value.description;

          // 处理异常图片上传
          if (exceptionForm.value.fileList.length > 0) {
            formData.exceptionImages = Array.isArray(exceptionForm.value.fileList) ? exceptionForm.value.fileList.join(',') : exceptionForm.value.fileList;
          }
        }

        // 5. 保存数据逻辑
        if (id.value) {
          // 编辑模式：先更新主表，再更新点检项

          // 5.1 更新主表
          const res = await deviceInspectionApi.update(formData);

          if (res.code === 200) {
            // 5.2 处理点检项
            await saveInspectionItems(id.value);

            createMessage.success('更新成功');
            // 触发reload事件，通知父页面刷新数据
            emit('reload');
            closePopup();
            return true;
          } else {
            createMessage.error(res.msg || '更新失败');
            return false;
          }
        } else {
          // 新增模式：先保存主表，获取ID后再保存点检项

          // 5.1 保存主表
          const res = await deviceInspectionApi.save(formData);

          if (res.code === 200 && res.data) {
            // 5.2 获取返回的ID
            const inspectionId = res.data.id;

            if (!inspectionId) {
              createMessage.error('获取点检记录ID失败');
              return false;
            }

            // 5.3 保存点检项
            await saveInspectionItems(inspectionId);

            createMessage.success('保存成功');
            // 触发reload事件，通知父页面刷新数据
            emit('reload');
            closePopup();
            return true;
          } else {
            createMessage.error(res.msg || '保存失败');
            return false;
          }
        }
      } catch (error) {
        console.error('保存数据失败', error);
        createMessage.error('保存数据失败，请稍后重试');
        return false;
      } finally {
        setPopupProps({ confirmLoading: false });
      }
    } catch (error) {
      console.error('表单验证失败', error);
      createMessage.error('表单验证失败，请检查输入');
      return false;
    }
  }

  // 保存点检项
  async function saveInspectionItems(inspectionId: string) {
    // 处理点检项数据
    const itemsData = [];
    inspectionItems.value.forEach(category => {
      category.items.forEach(item => {
        // 清除编辑状态标志和模板相关字段，不需要提交到后端
        const { isEditing, templateItemId, ...itemData } = item;

        delete itemData.id; // 确保不传递ID，让后端生成

        itemsData.push({
          ...itemData, // 保留所有其他属性
          inspectionId: inspectionId, // 关联到点检记录
          categoryId: category.id,
          categoryName: category.name,
          exceptionDesc: item.status === '2' ? item.exceptionDesc : '',
        });
      });
    });

    // 先删除原有点检项（编辑模式）
    if (id.value) {
      try {
        await deviceInspectionItemApi.removeByQuery({ inspectionId: inspectionId });
      } catch (error) {
        console.error('删除原有点检项失败', error);
        throw error; // 向上传递错误
      }
    }
    if (itemsData.length > 0) {
      // 批量保存新点检项
      const itemsRes = await deviceInspectionItemApi.saveOrUpdateBatch(itemsData);
      if (itemsRes.code !== 200) {
        throw new Error(itemsRes.msg || '保存点检项失败');
      }
    }
  }

  // 新增类别
  function handleAddCategory() {
    // 生成一个临时ID
    const tempId = 'temp_' + Date.now();

    // 创建新类别
    const newCategory = {
      id: tempId,
      name: '新增类别',
      items: [],
      isEditing: true, // 新增时默认进入编辑状态
    };

    // 添加到列表
    inspectionItems.value.push(newCategory);

    // 自动展开新类别
    activeCollapseKeys.value = [...activeCollapseKeys.value, tempId];
  }

  // 删除类别
  function handleDeleteCategory(index: number) {
    if (index >= 0 && index < inspectionItems.value.length) {
      const categoryName = inspectionItems.value[index].name;
      createConfirm({
        iconType: 'warning',
        title: '确认删除',
        content: `是否确认删除类别"${categoryName}"及其下所有点检项？`,
        onOk: () => {
          inspectionItems.value.splice(index, 1);
          createMessage.success('删除成功');
        },
      });
    }
  }

  // 新增点检项
  function handleAddItem(category: any) {
    if (!category || !category.items) return;

    // 生成一个临时ID
    const tempId = 'item_' + Date.now();

    // 创建新点检项
    const newItem = {
      id: tempId,
      name: '新增点检项',
      categoryId: category.id,
      categoryName: category.name,
      status: '1',
      exceptionDesc: '',
      isEditing: true, // 新增时默认进入编辑状态
    };

    // 添加到类别中
    category.items.push(newItem);
  }

  // 删除点检项
  function handleDeleteItem(category: any, itemIndex: number) {
    if (!category || !category.items || itemIndex < 0 || itemIndex >= category.items.length) return;

    const itemName = category.items[itemIndex].name;
    createConfirm({
      iconType: 'warning',
      title: '确认删除',
      content: `是否确认删除点检项"${itemName}"？`,
      onOk: () => {
        category.items.splice(itemIndex, 1);
        createMessage.success('删除成功');
      },
    });
  }

  // 编辑类别名称
  function handleEditCategoryName(category: any) {
    // 设置当前类别为编辑状态
    category.isEditing = true;

    // 阻止冒泡，防止折叠面板切换
    event?.stopPropagation();
  }

  onMounted(() => {
    fetchDeviceList();
  });
</script>

<style scoped lang="less">
  .inspection-form {
    padding: 10px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 24px 0 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .inspection-actions {
    margin-bottom: 16px;
    display: flex;
    gap: 8px;
  }

  .category-actions {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
  }

  .inspection-item-list {
    .inspection-item {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px dashed #f0f0f0;

      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }

      .inspection-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .inspection-item-name {
          font-weight: 500;
          flex: 1;
          cursor: pointer;

          span {
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;

            &:hover {
              background-color: #f9f9f9;
            }
          }
        }

        .inspection-item-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .inspection-item-exception {
        margin-top: 8px;
      }
    }
  }

  .fa-mb12 {
    margin-bottom: 12px;
  }

  .fa-mr12 {
    margin-right: 12px;
  }

  .category-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }
</style>
