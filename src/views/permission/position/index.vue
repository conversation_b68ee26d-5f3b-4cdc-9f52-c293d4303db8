<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left">
      <BasicLeftTree
        title="组织机构"
        :treeData="treeData"
        :loading="treeLoading"
        @reload="reloadTree"
        @select="handleTreeSelect"
        :dropDownActions="leftDropDownActions" />
    </div>
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" :searchInfo="searchInfo" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">{{ t('common.addText') }}</a-button>
            <a-button :disabled="selList.length === 0" @click="moveDept()">批量移动</a-button>
            <a-button :disabled="selList.length === 0" @click="batchRename()">批量重命名</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('common.importText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <MoveDeptForm @register="registerMoveDeptForm" @reload="reload" />
    <BatchRenameForm @register="registerBatchRenameForm" @reload="reload" />
    <CopyToDeptForm @register="registerCopyToDeptForm" @reload="reload" />
    <OrgTree @register="registerOrgTree" />
    <Member @register="registerMember" />
    <ImportModal @register="registerImportModal" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue';
import { BasicLeftTree, TreeItem } from '/@/components/Tree';
import { getPositionList, delPosition } from '/@/api/permission/position';
import { getDepartmentSelectorByAuth } from '/@/api/permission/organize';
import { BasicTable, useTable, TableAction, BasicColumn, FormProps, ActionItem } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { useModal } from '/@/components/Modal';
import { usePopup } from '/@/components/Popup';
import { useOrganizeStore } from '/@/store/modules/organize';
import Form from './Form.vue';
import Member from './Member.vue';
import OrgTree from '../user/OrgTree.vue';
import ImportModal from "./ImportModal.vue";
import { genDeleteBtn, genEditBtn, genQueryInput, genQueryKeyword } from "/@/utils/tableUtils";
import { positionApi } from "/@/api";
import MoveDeptForm from "./MoveDeptForm.vue";
import BatchRenameForm from "./BatchRenameForm.vue";
import CopyToDeptForm from "./CopyToDeptForm.vue";

defineOptions({ name: 'permission-position' });

const { createMessage } = useMessage();
const { t } = useI18n();
const organizeStore = useOrganizeStore();
const [registerMember, { openModal: openMemberModal }] = useModal();
const [registerForm, { openModal: openFormModal }] = useModal();
const [registerMoveDeptForm, { openModal: openMoveDeptFormModal }] = useModal();
const [registerBatchRenameForm, { openModal: openBatchRenameFormModal }] = useModal();
const [registerCopyToDeptForm, { openModal: openCopyToDeptFormModal }] = useModal();
const [registerOrgTree, { openPopup: openOrgTreePopup }] = usePopup();
const [registerImportModal, { openModal: openImportModal }] = useModal();

const columns: BasicColumn[] = [
  { title: '岗位名称', dataIndex: 'fullName', width: 200 },
  { title: '岗位编码', dataIndex: 'enCode', width: 150 },
  // { title: '岗位类型', dataIndex: 'type', width: 100 },
  { title: '所属组织', dataIndex: 'department' },
  { title: '创建时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm' },
  { title: '排序', dataIndex: 'sortCode', width: 70, align: 'center' },
];
const searchInfo = reactive({
  organizeId: '',
});
const leftDropDownActions = [
  {
    label: '架构图',
    onClick: openOrgTreePopup.bind(null, true, {}),
  },
];
const treeLoading = ref(false);
const treeData = ref<TreeItem[]>([]);
const organizeIdTree = ref([]);
const selList = ref<any[]>([]); // 选中的行
const [registerTable, { reload, setLoading, getForm }] = useTable({
  api: getPositionList,
  columns,
  immediate: false,
  useSearchForm: true,
  clickToRowSelect: true,
  rowSelection: { // 选中行
    onChange: (selectedRowKeys, selectedRows) => { // 选中行change
      console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: getFormConfig(),
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
});

function getFormConfig(): Partial<FormProps> {
  return {
    schemas: [
      genQueryKeyword(),
      genQueryInput('岗位名称', 'fullName'),
      genQueryInput('岗位编码', 'enCode'),
    ],
  };
}

function getTableActions(record): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
    {
      label: '复制',
      onClick: copyTo.bind(null, record.id),
    },
    {
      label: '岗位成员',
      onClick: viewMember.bind(null, record.id, record.fullName),
    },
  ];
}

function initData(isInit = false) {
  treeLoading.value = true;
  if (isInit) setLoading(true);
  getDepartmentSelectorByAuth().then(res => {
    treeData.value = res.data.list;
    treeLoading.value = false;
    if (isInit) reload({ page: 1 });
  });
}
function reloadTree() {
  treeData.value = [];
  initData();
}
function handleTreeSelect(id, _node, nodePath) {
  if (!id || searchInfo.organizeId === id) return;
  searchInfo.organizeId = id;
  organizeIdTree.value = nodePath.map(o => o.id);
  getForm().resetFields();
}
function addOrUpdateHandle(id = '') {
  openFormModal(true, { id, organizeId: searchInfo.organizeId, organizeIdTree: organizeIdTree.value });
}
function handleDelete(id) {
  delPosition(id).then(res => {
    createMessage.success(res.msg);
    organizeStore.resetState();
    reload();
  });
}
function viewMember(id, fullName) {
  openMemberModal(true, { id, fullName });
}

function copyTo(id) {
  openCopyToDeptFormModal(true, { id });
}

function moveDept() {
  openMoveDeptFormModal(true, { ids: selList.value });
}

function batchRename() {
  openBatchRenameFormModal(true, { ids: selList.value });
}

function handleImport() {
  openImportModal(true, {});
}

function handleExport() {
  let query = {
  };
  positionApi.exportExcel(query)
}

onMounted(() => {
  initData(true);
});
</script>
