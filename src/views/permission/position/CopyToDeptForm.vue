<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="复制岗位到指定部门" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #organizeId>
        <jnpfOrganizeSelect v-model:value="organizeIdTree" placeholder="选择所属组织" auth @change="onOrganizeChange" />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { useOrganizeStore } from '/@/store/modules/organize';
import { positionApi } from "/@/api";

const emit = defineEmits(['register', 'reload']);
const [registerForm, {setFieldsValue, resetFields, validate, clearValidate}] = useForm({
  schemas: [
    {
      field: 'organizeIds',
      label: '所属组织',
      component: 'DepSelect',
      componentProps: { placeholder: '选择所属组织', multiple: true, auth: true },
      rules: [{required: true, trigger: 'change', message: '所属组织不能为空',type: 'array'}],
    },
  ],
});
const [registerModal, {closeModal, changeOkLoading}] = useModalInner(init);
const id = ref('');
const organizeIdTree = ref([]);
const {createMessage} = useMessage();
const organizeStore = useOrganizeStore();

function init(data) {
  id.value = data.id
  organizeIdTree.value = []
  resetFields();
}

function onOrganizeChange(val) {
  setFieldsValue({ organizeId: !val || !val.length ? '' : val[val.length - 1] });
  if (!val || !val.length) return;
  clearValidate('organizeId');
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  positionApi.copyToDept(id.value, values.organizeIds).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    organizeStore.resetState();
    closeModal();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
