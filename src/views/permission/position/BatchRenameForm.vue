<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="批量重命名" @ok="handleSubmit">
    <BasicForm @register="registerForm">
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { useOrganizeStore } from '/@/store/modules/organize';
import { positionApi } from "/@/api";

const emit = defineEmits(['register', 'reload']);
const [registerForm, {resetFields, validate}] = useForm({
  schemas: [
    {
      field: 'fullName',
      label: '岗位名称',
      component: 'Input',
      componentProps: { placeholder: '输入名称', maxlength: 50 },
      rules: [
        { required: true, trigger: 'blur', message: '请输入岗位名称' },
      ],
    },
  ],
});
const [registerModal, {closeModal, changeOkLoading}] = useModalInner(init);
const ids = ref<string[]>([]);
const organizeIdTree = ref([]);
const {createMessage} = useMessage();
const organizeStore = useOrganizeStore();

function init(data) {
  ids.value = data.ids
  organizeIdTree.value = []
  resetFields();
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  positionApi.batchRename(ids.value, values.fullName).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    organizeStore.resetState();
    closeModal();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
