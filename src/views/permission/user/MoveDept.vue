<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="批量更新"
    :width="1000"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    destroyOnClose
    class="jnpf-import-modal">
    <!-- <div class="header-steps">
      <a-steps v-model:current="activeStep" type="navigation" size="small">
        <a-step title="批量更新" disabled />
      </a-steps>
    </div> -->
    <div class="import-main" v-show="activeStep == 0">
      <!-- 更新字段 -->

      <a-col :span="24" class="ant-col-item">
        <a-form-item name="account">
          <template #label>选择用户</template>
          <JnpfUserSelect
            v-model:value="dataForm.account"
            @change="changeData('account', -1)"
            placeholder="请选择"
            :allowClear="true"
            :style="{ width: '100%' }"
            :multiple="true"
            selectType="all">
          </JnpfUserSelect>
        </a-form-item>
      </a-col>
      <a-col :span="24" class="ant-col-item">
        <a-form-item name="organizeId">
          <template #label>选择部门</template>
          <JnpfDepSelect
            v-model:value="dataForm.organizeId"
            @change="changeData('organizeId', -1)"
            placeholder="请选择"
            :allowClear="true"
            :style="{ width: '100%' }"
            :showSearch="false"
            selectType="all">
          </JnpfDepSelect>
        </a-form-item>
      </a-col>
    </div>
    <template #insertFooter>
      <a-button @click="handleClose()" v-if="activeStep == 0">{{ t('common.cancelText') }}</a-button>
      <a-button @click="handlePrev" v-if="activeStep === 1" >{{ t('common.prev') }}</a-button>
      <a-button type="primary" @click="handleClose(true)" v-else danger>关闭</a-button>
      
      <a-button type="primary" @click="submitInfo">{{ t('提交') }}</a-button>

    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, toRefs } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useI18n } from '/@/hooks/web/useI18n';
  import type { UploadFile } from 'ant-design-vue';
  import { batchUpdateDept } from './helper/api';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface State {
    activeStep: number;
    fileName: string;
    fileList: UploadFile[];
    btnLoading: boolean;
    list: any[];
    result: any;
    resultList: any[];
    dataForm: any;
    tableRows: any;
    dataRule: any;
    optionsObj: any;
    childIndex: any;
    isEdit: any;
    interfaceRes: any;
    //可选范围默认值
    ableAll: any;
    title: string;
    continueText: string;
    allList: any[];
    currIndex: number;
    isContinue: boolean;
    submitType: number;
    showContinueBtn: boolean;
  }
  const emit = defineEmits(['register', 'reload']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const { t } = useI18n();
  const { createMessage } = useMessage();

  const state = reactive<State>({
    activeStep: 0,
    fileName: '',
    fileList: [],
    btnLoading: false,
    list: [],
    result: {},
    resultList: [],
    dataForm: {
      account: [],
      organizeId: undefined,
    },
    tableRows: {},
    dataRule: {},
    optionsObj: {},
    childIndex: -1,
    isEdit: false,
    interfaceRes: { organizeId: [], account: [] },
    //可选范围默认值
    ableAll: {},
    title: '',
    continueText: '',
    allList: [],
    currIndex: 0,
    isContinue: false,
    submitType: 0,
    showContinueBtn: true,
  });
  const { activeStep, dataForm } = toRefs(state);

  function init() {
    state.activeStep = 0;
    state.fileName = '';
    state.fileList = [];
    state.btnLoading = false;
  }
  function handlePrev() {
    if (state.activeStep == 0) return;
    state.activeStep -= 1;
  }

  function handleClose(reload = false) {
    closeModal();
    if (reload) emit('reload');
  }

  function submitInfo(){
    console.log("更新信息：",dataForm.value);
    batchUpdateDept(dataForm.value).then(res => {
      createMessage.success(res.msg);
      closeModal();
    });
  }

  function changeData(model, index) {
    state.isEdit = false;
    state.childIndex = index;
    for (let key in state.interfaceRes) {
      if (key != model) {
        let faceReList = state.interfaceRes[key];
        for (let i = 0; i < faceReList.length; i++) {
          let relationField = faceReList[i].relationField;
          if (relationField) {
            let modelAll = relationField.split('-');
            let faceMode = '';
            for (let i = 0; i < modelAll.length; i++) {
              faceMode += modelAll[i];
            }
            if (faceMode == model) {
              let options = 'get' + key + 'Options';
              eval(options)(true);
              changeData(key, index);
            }
          }
        }
      }
    }
  }
</script>
