<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="1600px" :minHeight="100" :showOkBtn="false">
    <template #insertFooter> </template>
    <table class="table-container">
      <thead>
        <tr>
          <th>姓名</th>
          <th>身份证</th>
          <th>证件编号</th>
          <th>证件名称</th>
          <th>发证单位</th>
          <th>工种</th>
          <th>职业资格</th>
          <th>技能等级</th>
          <th>发证日期</th>
          <th>复审日期</th>
          <th>到期日期</th>
          <th>进场日期</th>
          <th>退场日期</th>
          <th>审核结果</th>
          <th>职业健康体检</th>
          <th>来源单位</th>
          <th>部门</th>
          <th>准驾车型</th>
          <th>工龄</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in dataForm" :key="index">
          <td>{{ item.name }}</td>
          <td>{{ item.idCard }}</td>
          <td>{{ item.doctNumber }}</td>
          <td>{{ item.doctName }}</td>
          <td>{{ item.issuingUnit }}</td>
          <td>{{ item.job }}</td>
          <td>{{ item.professionalQualifications }}</td>
          <td>{{ item.skillLevel }}</td>
          <td>{{ new Date(item.issueDate).toLocaleString() }}</td>
          <td>{{ new Date(item.reviewDate).toLocaleString() }}</td>
          <td>{{ new Date(item.expireDate).toLocaleString() }}</td>
          <td>{{ new Date(item.approachDate).toLocaleString() }}</td>
          <td>{{ new Date(item.exitDate).toLocaleString() }}</td>
          <td>{{ item.auditResults }}</td>
          <td>{{ item.occupationalHealth }}</td>
          <td>{{ item.sourceUnit }}</td>
          <td>{{ item.dept }}</td>
          <td>{{ item.drivingType }}</td>
          <td>{{ item.seniority }}</td>
        </tr>
      </tbody>
    </table>
  </BasicModal>
  <!-- 有关联表单详情：开始 -->
  <RelationDetail ref="relationDetailRef" />
  <!-- 有关联表单详情：结束 -->
</template>
<script lang="ts" setup>
  import { getDetailInfo } from './helper/api';
  import { getConfigData } from '/@/api/onlineDev/visualDev';
  import { reactive, toRefs, nextTick, ref, computed, unref, toRaw } from 'vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  // 有关联表单详情
  import RelationDetail from '/@/views/common/dynamicModel/list/detail/index.vue';
  // 表单权限
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useMessage } from '/@/hooks/web/useMessage';

  interface State {
    dataForm: any;
    title: string;
  }

  defineOptions({ name: 'Detail' });
  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal, setModalProps, closeModal }] = useModal();

  const relationDetailRef = ref<any>(null);
  const state = reactive<State>({
    dataForm: {},
    title: '详情',
  });
  const { title, dataForm } = toRefs(state);
  // 表单权限
  const { hasFormP } = usePermission();

  defineExpose({ init });

  function init(data) {
    state.dataForm.id = data.id;
    openModal();
    nextTick(() => {
      setTimeout(initData, 0);
    });
  }
  function initData() {
    changeLoading(true);
    if (state.dataForm.id) {
      getData(state.dataForm.id);
    } else {
      closeModal();
    }
  }
  function getData(id) {
    getDetailInfo(id).then(res => {
      state.dataForm = res.data || {};
      nextTick(() => {
        changeLoading(false);
      });
    });
  }

  function toDetail(modelId, id) {
    if (!id) return;
    getConfigData(modelId).then(res => {
      if (!res.data || !res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = 'general';
      const data = { id, formConf, modelId };
      relationDetailRef.value?.init(data);
    });
  }
  function setFormProps(data) {
    setModalProps(data);
  }
  function changeLoading(loading) {
    setFormProps({ loading });
  }
</script>
