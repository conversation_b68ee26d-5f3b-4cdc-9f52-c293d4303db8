<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="link" @click="handelBatchManage()">批量管理班组</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <MoveDept @register="registerMoveDept" @reload="reload" />
    <Form @register="registerForm" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useI18n } from '/@/hooks/web/useI18n';
  import * as TeamApi from '/@/api/permission/team';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePopup } from '/@/components/Popup';
  import Form from './Form.vue';
  import MoveDept from './MoveDept.vue';
  import { genQueryInput, genQueryKeyword } from '/@/utils/tableUtils';
  import { useModal } from '/@/components/Modal';

  defineOptions({ name: 'base_user' });

  const { t } = useI18n();
  const { createMessage, createConfirm } = useMessage();
  const columns: BasicColumn[] = [
    // { title: '序号', dataIndex: 'id', width: 80 },
    { title: '来源单位', dataIndex: 'sourceUnit' },
    { title: '姓名', dataIndex: 'realName' },
    { title: '部门', dataIndex: 'organizeId' },
    { title: '班组', dataIndex: 'team' },
    { title: '手机', dataIndex: 'mobilePhone' },
    { title: '身份证号', dataIndex: 'identificationNumber' },
  ];
  
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerMoveDept, { openModal: openMoveDeptModal }] = useModal();
  const [registerTable, { reload, getFetchParams, getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    api: TeamApi.page,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQueryKeyword(),
        genQueryInput('来源单位', 'sourceUnit'),
        genQueryInput('姓名', 'realName'),
        genQueryInput('部门', 'organizeId'),
        genQueryInput('班组', 'team'),
        genQueryInput('手机', 'mobilePhone'),
        genQueryInput('身份证号', 'identificationNumber'),
      ],
    },
    actionColumn: {
      width: 90,
      title: '操作',
      dataIndex: 'action',
    },
    // 行选择
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: record => ({ disabled: !!record.top }),
    },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
    ];
  }

  function addOrUpdateHandle(id = '') {
    // console.log('id:',id);
    openFormPopup(true, { id });
    clearSelectedRowKeys();
  }

  // 批量管理
  function handelBatchManage() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要批量移动吗, 是否继续?',
      onOk: () => {
        console.log('ids:',ids);
        openMoveDeptModal(true, {ids});
        clearSelectedRowKeys();
        reload();
      },
    });
  }
</script>
