<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px">
    </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { genInput } from "/@/utils/formUtils";
import * as TeamApi from '/@/api/permission/team';

const id = ref('');

const schemas: FormSchema[] = [
  genInput('来源单位', 'sourceUnit'),
  genInput('姓名', 'realName'),
  genInput('部门', 'organizeId'),
  genInput('班组', 'team'),
  genInput('手机', 'mobilePhone'),
  genInput('身份证号', 'identificationNumber'),
];
const getTitle = computed(() => (!unref(id) ? '新建班组人员' : '编辑班组人员'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields}] = useForm({labelWidth: 120, schemas: schemas});
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

async function init(data:any) {
  resetFields();
  id.value = data.id;
  if (id.value) {
    changeLoading(true);
    TeamApi.getTeamById(id.value).then(res => {
      setFieldsValue(res.data);
      changeLoading(false);
    });
  }
}

function handleFieldValueChange(field:any, value:any) {
  console.log('field', field, 'value', value);
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const data = {
    ...values,
    id: id.value,
  };
  const formMethod = id.value ? TeamApi.updateTeam : TeamApi.saveTeam;
  formMethod(data)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closePopup();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
