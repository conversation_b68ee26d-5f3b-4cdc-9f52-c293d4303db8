<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- <a-button type="link" preIcon="icon-ym icon-ym-btn-download" @click="openExportModal(true, { columnList: state.exportList })">导出</a-button> -->
<!--            <a-button type="link" preIcon="icon-ym icon-ym-btn-upload" @click="openImportModal(true, { url: 'permission/Organize' })">导入</a-button>-->
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('common.importText') }}</a-button>
            <a-button @click="syncOldData()" :loading="loadingSync">同步老数据</a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleAdd">
                  <a-menu-item key="company">新建公司</a-menu-item>
                  <a-menu-item key="department">新建部门</a-menu-item>
                </a-menu>
              </template>
              <a-button type="primary" preIcon="icon-ym icon-ym-btn-add">{{ t('common.addText') }}
                <DownOutlined/>
              </a-button>
            </a-dropdown>
            <a-button @click="handleBatchUpdate">批量修改</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fullName'"><i :class="'mr-6px ' + record.icon"></i>{{ record.fullName }}</template>
            <template v-if="column.key === 'type'">
              <a-tag color="#f50" v-if="record.type === 'company'">公司</a-tag>
              <a-tag color="#2db7f5" v-if="record.type === 'department'">部门</a-tag>
              <a-tag color="#87d068" v-if="record.type === 'team'">班组</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" :dropDownActions="getDropDownActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
    <DepForm @register="registerDepForm" @reload="reload"/>
    <DepBatchUpdateForm @register="registerDepBatchUpdateForm" @reload="reload"/>
    <Member @register="registerMember"/>
    <ExportModal @register="registerExportModal" @download="handleDownload"/>
    <ImportModal @register="registerImportModal" @reload="reload"/>
  </div>
</template>
<script lang="ts" setup>
import { delOrganize, getOrganizeList } from '/@/api/permission/organize';
import { ActionItem, BasicColumn, BasicTable, FormProps, TableAction, useTable } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { useI18n } from '/@/hooks/web/useI18n';
import { useModal } from '/@/components/Modal';
import { usePopup } from '/@/components/Popup';
import { ExportModal } from '/@/components/CommonModal';
import   ImportModal  from './ImportModal.vue';
import { useOrganizeStore } from '/@/store/modules/organize';
import Form from './Form.vue';
import DepForm from './DepForm.vue';
import DepBatchUpdateForm from './DepBatchUpdateForm.vue';
import Member from './Member.vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { downloadByUrl } from '/@/utils/file/download';
// import { reactive } from 'vue';
import { exportData } from './helper/api';
import { isNil } from "lodash-es";
import {organizeTreeApi, testEduFileApi} from "/@/api";
import {ref} from "vue";

defineOptions({name: 'permission-organize'});

const {createMessage} = useMessage();
const {t} = useI18n();
const organizeStore = useOrganizeStore();
const [registerDepForm, {openModal: openDepFormModal}] = useModal();
const [registerDepBatchUpdateForm, {openModal: openDepBatchUpdateFormModal}] = useModal();
const [registerMember, {openModal: openMemberModal}] = useModal();
const [registerForm, {openPopup: openFormPopup}] = usePopup();
const [registerExportModal, {closeModal: closeExportModal, setModalProps: setExportModalProps}] = useModal();
const [registerImportModal, {openModal: openImportModal}] = useModal();
const loadingSync = ref(false)

const columns: BasicColumn[] = [
  {title: '名称', dataIndex: 'fullName'},
  {title: '公司编号', dataIndex: 'enCode'},
  {title: '编码', dataIndex: 'code', width: 100 },
  {title: '层级', dataIndex: 'index', width: 100, align: 'center'},
  {title: '类型', dataIndex: 'type', width: 100, align: 'center', customRender: ({record}) => (record.type === 'company' ? '公司' : record.type === 'department' ? '部门' : '班组')},
  {title: '创建时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
  {title: '排序', dataIndex: 'sortCode', width: 70, align: 'center'},
];
const [registerTable, {getFetchParams, getSelectRowKeys, reload}] = useTable({
  api: getOrganizeList,
  columns,
  isTreeTable: true,
  useSearchForm: true,
  pagination: false,
  formConfig: getFormConfig(),
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
  defaultExpandAllRows: false,
  afterFetch: data => setTableIndex(data),
  rowSelection: { type: 'checkbox' },
});

// 树形列表index层级
function setTableIndex(arr, index = 0) {
  arr.forEach(item => {
    item.index = 1;
    if (index) item.index = index + 1;
    if (item.children) setTableIndex(item.children, item.index);
  });
}

function getFormConfig(): Partial<FormProps> {
  return {
    schemas: [
      {
        field: 'keyword',
        label: t('common.keyword'),
        component: 'Input',
        componentProps: {
          placeholder: t('common.enterKeyword'),
          submitOnPressEnter: true,
        },
      },
    ],
  };
}

function getTableActions(record): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id, record.type, record.parentId),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function getDropDownActions(record): ActionItem[] {
  return [
    {
      label: '查看成员',
      onClick: viewMember.bind(null, record.id, record.fullName),
    },
  ];
}

function handleAdd({key}) {
  addOrUpdateHandle('', key);
}

function addOrUpdateHandle(id = '', type, parentId = '') {
  const openMethod = type === 'company' ? openFormPopup : openDepFormModal;
  openMethod(true, {id, parentId});
}

async function syncOldData() {
  try {
    loadingSync.value = true
    await organizeTreeApi.syncOldData()
    loadingSync.value = false
    createMessage.success('同步成功');
  } catch (e) {
    loadingSync.value = false
  }
}

function handleImport() {
  openImportModal(true, {});
}

function handleDelete(id) {
  delOrganize(id).then(res => {
    createMessage.success(res.msg);
    organizeStore.resetState();
    reload();
  });
}

function viewMember(id, fullName) {
  openMemberModal(true, {id, fullName});
}

function handleDownload(data) {
  let query = {...getFetchParams(), ...data};
  exportData(query)
    .then(res => {
      setExportModalProps({confirmLoading: false});
      if (!res.data.url) return;
      downloadByUrl({url: res.data.url});
      closeExportModal();
    })
    .catch(() => {
      setExportModalProps({confirmLoading: false});
    });
}

function handleBatchUpdate() {
  const ids = getSelectRowKeys();
  console.log('ids', ids)
  if (isNil(ids) || ids.length === 0) {
    createMessage.warning('请先选择组织');
    return;
  }
  openDepBatchUpdateFormModal(true, { ids })
}
</script>
