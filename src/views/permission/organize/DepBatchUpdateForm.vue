<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="批量修改" @ok="handleSubmit">
    <BasicForm @register="registerForm">
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
import { organizeTreeApi } from '/@/api';
import { ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { BASE_ORGANIZE_OPTIONS } from "/@/enums/zzEnums";
import { genSelect } from "/@/utils/formUtils";


const emit = defineEmits(['register', 'reload']);
const [registerForm, {resetFields, validate}] = useForm({
  schemas: [
    genSelect('机构分类', 'category', true, 'string', BASE_ORGANIZE_OPTIONS),
  ],
});
const [registerModal, {closeModal, changeOkLoading}] = useModalInner(init);
const ids = ref([]);
const {createMessage} = useMessage();


function init(data: any) {
  resetFields();
  ids.value = data.ids;
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ids: ids.value,
    ...values,
  };
  organizeTreeApi.updateBatchCategory(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closeModal();
    setTimeout(() => {
      emit('reload');
    }, 300);
  }).catch(() => changeOkLoading(false));
}
</script>
