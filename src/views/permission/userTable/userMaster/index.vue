<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">{{ t('common.addText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'gender'">
              <FaDictShow :value="record.gender" dict-name="sex" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { userMasterApi as api } from '/@/api';
import { useI18n } from "/@/hooks/web/useI18n";
import { usePopup } from "/@/components/Popup";
import Form from "./Form.vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { genQueryInput, genQuerySearch } from "/@/utils/tableUtils";


defineOptions({ name: 'permission-userTable-userMaster' });

const { t } = useI18n();
const {createMessage} = useMessage();
const [registerForm, {openPopup: openFormPopup}] = usePopup();
const columns: BasicColumn[] = [
  { title: '来源单位', dataIndex: 'sourceUnit', width: 200 },
  { title: '姓名', dataIndex: 'realName', width: 100 },
  { title: '性别', dataIndex: 'gender', width: 100 },
  { title: '部门', dataIndex: 'organizeName', width: 100 },
  { title: '岗位', dataIndex: 'positionId', width: 100 },
  { title: '身份证号', dataIndex: 'identificationNumber', width: 180 },
  { title: '手机', dataIndex: 'mobilePhone', width: 120 },
  { title: '培训时间', dataIndex: 'trainingTime', width: 120, format: 'date|YYYY-MM-DD' },
  { title: '生日', dataIndex: 'birthday', width: 120, format: 'date|YYYY-MM-DD' },
  { title: '班组', dataIndex: 'team', width: 100 },
  { title: '年龄', dataIndex: 'age', width: 100 },
  { title: '民族', dataIndex: 'nation', width: 100 },
  { title: '学历', dataIndex: 'education', width: 100 },
  { title: '籍贯', dataIndex: 'nativePlace', width: 100 },
  { title: '人员类别', dataIndex: 'categoryPersonnel', width: 100 },
  { title: '进入项目时间', dataIndex: 'goProjectTime', width: 120, format: 'date|YYYY-MM-DD' },
  { title: '血型', dataIndex: 'bloodType', width: 100 },
  { title: '用工形式', dataIndex: 'formEmployment', width: 100 },
  { title: '户口性质', dataIndex: 'natureAccount', width: 100 },
  { title: '备注', dataIndex: 'remark', width: 100 },
  // { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('名称', 'name'),
    ],
  },
  actionColumn: {
    width: 90,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id, record.parentId),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function addOrUpdateHandle(id='') {
  openFormPopup(true, {id});
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
