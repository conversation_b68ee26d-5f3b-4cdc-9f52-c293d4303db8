<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit" :destroyOnClose="true" defaultFullscreen class="jnpf-full-modal full-modal">
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { organizeTreeApi, userMasterApi as api } from '/@/api';
import { genCommon, genDictSelect, genInput } from "/@/utils/formUtils";
import { trim } from 'lodash-es'


const id = ref('');
const { createMessage } = useMessage();
const emit = defineEmits(['register', 'reload']);
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    genInput('来源单位', 'sourceUnit'),
    genInput('姓名', 'realName'),
    genDictSelect('性别', 'gender', true, 'sex'),
    {
      ...genCommon('部门', 'organizeId', 'FaCascader'),
      componentProps: { api: organizeTreeApi, showRoot: false },
    },
  ],
});
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

const getTitle = computed(() => (!unref(id) ? '新建数据' : '编辑数据'));

function init(data) {
  resetFields();
  id.value = data.id;
  if (id.value) {
    changeLoading(true);
    api.getById(id.value).then(res => {
      setFieldsValue({
        ...res.data,
        gender: trim(res.data.gender),
        organizeIds: [[res.data.organizeId]]
      });
      changeLoading(false);
    });
  }
}

function onOrgChange(val) {
  console.log('onOrgChange', val)
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  // changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
  };
  console.log('query', query)
  // const formMethod = id.value ? api.update : api.save;
  // formMethod(query).then(res => {
  //   createMessage.success(res.msg);
  //   changeOkLoading(false);
  //   closePopup();
  //   emit('reload');
  // }).catch(() => changeOkLoading(false));
}
</script>
