<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
<!--            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新建</a-button>-->
            <a-button @click="syncHandle()" type="primary">同步</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { zzUserCertificateApi as api } from '/@/api';
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch, genQuerySelectBool } from "/@/utils/tableUtils";
import Form from "./Form.vue";


defineOptions({ name: 'permission-userCertificate' });

const {createMessage, simpleConfirm} = useMessage();
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const columns: BasicColumn[] = [
  { title: '姓名', dataIndex: 'userRealName', width: undefined },
  // { title: '监护证', dataIndex: 'certGuardian', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '动火作业', dataIndex: 'certGuardianFire', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '高处作业', dataIndex: 'certGuardianHighwork', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '受限空间', dataIndex: 'certGuardianLimitation', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '起重吊装', dataIndex: 'certGuardianLift', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '脚手架', dataIndex: 'certGuardianScaffold', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '临时证', dataIndex: 'certTemp', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  { title: '正式证', dataIndex: 'certOfficial', width: 100, customRender: ({value}) => value ? '✅' : '/' },
  // { title: '创建时间', dataIndex: 'creatorTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
];
const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('名称', 'userRealName'),
      genQuerySelectBool('动火作业', 'certGuardianFire'),
      genQuerySelectBool('高处作业', 'certGuardianHighwork'),
      genQuerySelectBool('受限空间', 'certGuardianLimitation'),
      genQuerySelectBool('起重吊装', 'certGuardianLift'),
      genQuerySelectBool('脚手架', 'certGuardianScaffold'),
      genQuerySelectBool('临时证', 'certTemp'),
      genQuerySelectBool('正式证', 'certOfficial'),
    ],
  },
  // actionColumn: {
  //   width: 90,
  //   title: '操作',
  //   dataIndex: 'action',
  // },
});

function getTableActions(record:any): ActionItem[] {
  return [
    // genEditBtn(record, addOrUpdateHandle),
    // genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id='') {
  openFormPopup(true, {id});
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

async function syncHandle() {
  await simpleConfirm('同步会立刻同步全部账户的资政数据，同步需要一定时间，请耐心等待，是否开始同步？')
  api.syncAll().then(res => {
    createMessage.success('同步成功');
    reload();
  });
}
</script>
