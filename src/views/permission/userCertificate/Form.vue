<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit" :destroyOnClose="true">
    <BasicForm @register="registerForm" />
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from "/@/components/Popup";
import { BasicForm, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { zzDemoTreeApi, zzDemoTreeTableApi as api } from '/@/api';
import { genCommon, genInput } from "/@/utils/formUtils";

const id = ref('');

const getTitle = computed(() => (!unref(id) ? '新建数据' : '编辑数据'));
const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
  schemas: [
    {
      ...genCommon('所属节点', 'treeId', 'FaCascader'),
      componentProps: { api: zzDemoTreeApi, showRoot: true },
    },
    genInput('数据名称', 'name'),
  ],
});
const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

function init(data) {
  resetFields();
  id.value = data.id;
  setFieldsValue({ treeId: data.treeId })
  if (id.value) {
    changeLoading(true);
    api.getById(id.value).then(res => {
      setFieldsValue(res.data);
      changeLoading(false);
    });
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
  };
  const formMethod = id.value ? api.update : api.save;
  formMethod(query).then(res => {
    createMessage.success(res.msg);
    changeOkLoading(false);
    closePopup();
    emit('reload');
  }).catch(() => changeOkLoading(false));
}
</script>
