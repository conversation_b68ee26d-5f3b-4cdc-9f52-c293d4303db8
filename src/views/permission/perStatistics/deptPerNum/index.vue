<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart ref="chartRef" :options="options" class="mt-30px" height="500px" />
      <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { getDeptPer, getJuniorDeptPer } from '/@/api/basic/user';
  import { reactive, onMounted, ref } from 'vue';
  import { Chart } from '/@/components/Chart';

  defineOptions({ name: 'extend-graphDemo-echartsBar' });

  const chartRef = ref<any>(null); // ref

  const options = ref({
    title: {
      text: '部门人员数量',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
      },
    ],
  });

  const chartRef2 = ref<any>(null);
  const options2 = ref({
    title: {
      text: '部门下级人员数量',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
      },
    ],
  });

  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      // 替换为您的后台API端点，获取数据
      const response = await getDeptPer();
      const departments = response.data;

      // 赋值
      let nameArr = departments.map(item => item.name);
      let countArr = departments.map(item => ({
        value: item.count,
        name: item.name,
        id: item.id,
      }));
      options.value.xAxis.data = nameArr;
      options.value.series[0].data = countArr;

      setTimeout(() => {
        console.log(chartRef.value.getInstance());
        const instance = chartRef.value.getInstance();
        if (instance) {
          instance.on('click', function (params) {
            console.log('点击了柱状图，数据索引为：', params);
            const deptId = params.data.id;
            getJuniorDeptPer(deptId).then(res => {

              const juniorDepartments = res.data;

              // 赋值
              let juniorNameArr = juniorDepartments.map(item => item.name);
              let juniorCountArr = juniorDepartments.map(item => ({
                value: item.count,
                name: item.name,
                id: item.id,
              }));

              options2.value.xAxis.data = juniorNameArr;
              options2.value.series[0].data = juniorCountArr;
            });
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error fetching department data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });
</script>

<style lang="less" scoped>
  .jnpf-content-wrapper-center {
    overflow-y: auto;
  }
</style>
