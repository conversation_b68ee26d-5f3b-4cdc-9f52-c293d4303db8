<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart :options="options" class="mt-30px" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { reactive, onMounted, ref } from 'vue';
  import { Chart } from '/@/components/Chart';
  import { getPerPlace } from '/@/api/basic/user';

  defineOptions({ name: 'extend-graphDemo-echartsPie' });

  const options = ref({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    series: [
      {
        name: '籍贯',
        type: 'pie',
        radius: ['40%', '55%'],
        label: {
          formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
          backgroundColor: '#eee',
          borderColor: '#aaa',
          borderWidth: 1,
          borderRadius: 4,
          rich: {
            a: {
              color: '#999',
              lineHeight: 22,
              align: 'center',
            },
            hr: {
              borderColor: '#aaa',
              width: '100%',
              borderWidth: 0.5,
              height: 0,
            },
            b: {
              fontSize: 16,
              lineHeight: 33,
            },
            per: {
              color: '#eee',
              backgroundColor: '#334455',
              padding: [2, 4],
              borderRadius: 2,
            },
          },
        },
        data: [],
      },
    ],
  });

  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      // 替换为您的后台API端点，获取数据
      const response = await getPerPlace();
      const departments = response.data;

      // 赋值
      // let ageArr = departments.map(item=>item.age)
      // let countArr = departments.map(item=>item.count)
      // options.value.xAxis.data = ageArr;
      options.value.series[0].data = departments;
    } catch (error) {
      console.error('Error fetching department data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });
</script>
