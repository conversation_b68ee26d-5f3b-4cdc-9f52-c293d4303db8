import { defHttp } from '/@/utils/http/axios';

// 获取列表
export function getList(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/getRemoveList', data });
}
// 新建
export function create(data) {
  return defHttp.post({ url:'/api/example/RemoveUser', data });
}
// 修改
export function update(data) {
  return defHttp.put({ url: '/api/example/RemoveUser/'+ data.id, data });
}
// 详情(无转换数据)
export function getInfo(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/' + id });
}
// 获取(转换数据)
export function getDetailInfo(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/detail/' + id });
}
// 恢复
export function del(id) {
  return defHttp.get({ url: '/api/example/RemoveUser/recover/' + id });
}
// 批量恢复
export function batchDelete(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/batchRecover', data });
}
// 导出
export function exportData(data) {
  return defHttp.post({ url: '/api/example/RemoveUser/Actions/Export', data });
}
