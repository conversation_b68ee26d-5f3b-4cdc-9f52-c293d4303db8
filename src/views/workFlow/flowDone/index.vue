<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'fullName'">
              <a-tag color="success" v-if="record.delegateUser">委托</a-tag>
              {{ record.fullName }}
            </template>
            <template v-if="column.key === 'status'">
              <a-tag color="success" v-if="record.status == 1">通过</a-tag>
              <a-tag color="processing" v-else-if="record.status == 10">前加签</a-tag>
              <a-tag color="error" v-else>退回</a-tag>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <FlowParser @register="registerFlowParser" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { getFlowBeforeList } from '/@/api/workFlow/flowBefore';
  import { getFlowEngineListAll, getFlowList } from '/@/api/workFlow/flowEngine';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePopup } from '/@/components/Popup';
  import { useBaseStore } from '/@/store/modules/base';
  import FlowParser from '/@/views/workFlow/components/FlowParser.vue';
  import { useDefineSetting } from '/@/hooks/setting/useDefineSetting';

  defineOptions({ name: 'workFlow-flowDone' });

  const { createMessage } = useMessage();
  const baseStore = useBaseStore();
  const { t } = useI18n();
  const { flowUrgentList, getUrgentText } = useDefineSetting();
  const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();

  const columns: BasicColumn[] = [
    { title: '流程标题', dataIndex: 'fullName', width: 200 },
    { title: '所属流程', dataIndex: 'flowName', width: 150 },
    { title: '发起时间', dataIndex: 'startTime', width: 150, format: 'date|YYYY-MM-DD HH:mm' },
    { title: '发起人员', dataIndex: 'userName', width: 120 },
    { title: '审批节点', dataIndex: 'thisStep', width: 150 },
    { title: '紧急程度', dataIndex: 'flowUrgent', width: 100, align: 'center' },
    { title: '流程状态', dataIndex: 'status', width: 120, align: 'center' },
    { title: '办理时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm' },
  ];
  const [registerTable, { reload, getForm }] = useTable({
    api: getFlowBeforeList,
    columns,
    searchInfo: { category: 2 },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: { placeholder: t('common.enterKeyword'), submitOnPressEnter: true },
        },
        {
          field: 'pickerVal',
          label: '日期',
          component: 'DateRange',
        },
        {
          field: 'flowCategory',
          label: '分类',
          component: 'Select',
          componentProps: { placeholder: '请选择分类', showSearch: true },
        },
        {
          field: 'templateId',
          label: '所属流程',
          component: 'Select',
          componentProps: { placeholder: '选择所属流程', showSearch: true, fieldNames: { options: 'children' }, onChange: onTemplateIdChange },
        },
        {
          field: 'flowId',
          label: '所属名称',
          component: 'Select',
          componentProps: { placeholder: '选择所属名称', showSearch: true, onDropdownVisibleChange: visibleFlowChange },
        },
        {
          field: 'creatorUserId',
          label: '发起人员',
          component: 'UserSelect',
          componentProps: { placeholder: '选择发起人员' },
        },
        {
          field: 'flowUrgent',
          label: '紧急程度',
          component: 'Select',
          componentProps: { placeholder: '选择紧急程度', showSearch: true, options: flowUrgentList },
        },
      ],
      fieldMapToTime: [['pickerVal', ['startTime', 'endTime']]],
      resetFunc: async () => {
        getForm().updateSchema({ field: 'flowId', componentProps: { options: [] } });
      },
    },
    actionColumn: {
      width: 50,
      title: '操作',
      dataIndex: 'action',
    },
    afterFetch: data => {
      const list = data.map(o => {
        o.flowUrgent = getUrgentText(o.flowUrgent);
        return o;
      });
      return list;
    },
  });

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: toDetail.bind(null, record),
      },
    ];
  }
  function toDetail(record) {
    const data = {
      id: record.processId,
      flowId: record.flowId,
      opType: 2,
      taskNodeId: record.thisStepId,
      taskId: record.id,
    };
    openFlowParser(true, data);
  }
  function onTemplateIdChange(val) {
    getForm().setFieldsValue({ flowId: '' });
    getForm().updateSchema({ field: 'flowId', componentProps: { options: [] } });
    if (!val) return;
    getFlowOptions(val);
  }
  function visibleFlowChange(val) {
    if (!val) return;
    const values = getForm().getFieldsValue();
    if (!values.templateId) createMessage.warning('请先选择所属流程');
  }
  function getFlowOptions(val) {
    getFlowList(val).then(res => {
      getForm().updateSchema({ field: 'flowId', componentProps: { options: res.data } });
    });
  }
  function getFlowEngineList() {
    getFlowEngineListAll().then(res => {
      const options = res.data.list.filter(o => o.children && o.children.length);
      getForm().updateSchema({ field: 'templateId', componentProps: { options } });
    });
  }
  async function getOptions() {
    const res = await baseStore.getDictionaryData('WorkFlowCategory');
    getForm().updateSchema({ field: 'flowCategory', componentProps: { options: res } });
    getFlowEngineList();
  }
  onMounted(() => {
    getOptions();
  });
</script>
