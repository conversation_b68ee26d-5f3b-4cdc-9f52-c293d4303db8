<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="state.title" @ok="handleSubmit" :minHeight="252">
    <a-form :colon="false" :labelCol="{ style: { width: '80px' } }" :model="dataForm" ref="formElRef">
      <a-form-item label="手写签名" name="signImg" v-if="properties.hasSign" :rules="[{ required: true, message: `请签名`, trigger: 'change' }]">
        <jnpf-sign v-model:value="dataForm.signImg" submitOnConfirm :isDefault="1"/>
      </a-form-item>
      <a-form-item label="人脸识别" name="faceSignId" v-if="properties.hasFace" :rules="[{ required: true, message: `请进行人脸识别`, trigger: 'change' }]">
        <div>
          <Image v-if="dataForm.faceSignFileId" :width="80" :src="fileSaveApi.getFileLocal(dataForm.faceSignFileId)" :preview="false" class="cursor-pointer" @click="handlePreview(fileSaveApi.getFileLocal(dataForm.faceSignFileId))" />

          <a-popover v-model:visible="visible" title="人脸识别" trigger="click">
            <template #content>
              <div class="container">
                <canvas id="qrcode" ref="qrCodeRef"></canvas>
                <p class="tips">使用数智协同App扫一扫</p>
                <p class="tips">在App上人脸识别签到成功后，点击下方确认按钮</p>
                <a-button @click="handleQueryFaceSignResult">确认人脸识别成功</a-button>
              </div>
            </template>
            <a-button>APP扫码进行人脸识别</a-button>
          </a-popover>
        </div>
      </a-form-item>
    </a-form>
  </BasicModal>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref, toRefs, unref, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { FormInstance, Image } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user';
import { toCanvas } from "qrcode";
import { buildUUID } from '/@/utils/uuid';
import { fileSaveApi, zzUserSignFaceApi } from '/@/api';
import { useMessage } from "/@/hooks/web/useMessage";
import { createImgPreview } from "/@/components/Preview";

interface State {
  dataForm: any;
  properties: any;
  eventType: string;
  title: string;
  label: string;
  faceSignBuzzId: string;
}

const emit = defineEmits(['register', 'confirm']);
const userStore = useUserStore();
const formElRef = ref<FormInstance>();
const visible = ref<boolean>(false);
const qrCodeRef = ref();
const state = reactive<State>({
  dataForm: {
    signImg: '',
    faceSignId: '',
    faceSignFileId: '',
  },
  properties: {},
  eventType: '',
  title: '',
  label: '',
  faceSignBuzzId: '',
});
const {dataForm, properties} = toRefs(state);
const [registerModal, {changeOkLoading}] = useModalInner(init);
const { createMessage } = useMessage();

const getUserInfo = computed(() => userStore.getUserInfo || {});

function init(data) {
  changeOkLoading(false);
  state.dataForm = {
    signImg: '',
    faceSignId: '',
    faceSignFileId: '',
  };
  state.properties = data.properties;
  state.eventType = data.eventType;
  if (state.properties.hasSign) state.dataForm.signImg = unref(getUserInfo).signImg;
  switch (data.eventType) {
    case 'submit':
      state.title = '您确定要提交当前流程吗, 是否继续?';
      state.label = '提交';
      break;
    default:
      break;
  }
  state.faceSignBuzzId = buildUUID()
  nextTick(() => {
    formElRef.value?.clearValidate();
  });
}

function getQrcode() {
  const qrcode = JSON.stringify({t: 'faceSign', buzzType: 'flow', buzzId: state.faceSignBuzzId})
  toCanvas(qrCodeRef.value, qrcode, {
    margin: 0,
    width: 265,
  });
}

function handleQueryFaceSignResult() {
  zzUserSignFaceApi.list({
    buzzType: 'flow',
    buzzId: state.faceSignBuzzId
  }).then(res => {
    if (res.data && res.data[0]) {
      const data = res.data[0]
      state.dataForm.faceSignId = data.id
      state.dataForm.faceSignFileId = data.facePhotoId
      visible.value = false
      createMessage.success("人脸识别成功")
    } else {
      createMessage.error("未找到人脸识别记录，请确认")
    }
  })
}

async function handleSubmit() {
  try {
    const values = await formElRef.value?.validate();
    if (!values) return;
    changeOkLoading(true);
    emit('confirm', state.dataForm);
  } catch (_) {
  }
}

function handlePreview(img) {
  createImgPreview({ imageList: [img] });
}

watch(() => visible.value, val => {
  setTimeout(() => {
    getQrcode();
  }, 300)
})
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#qrcode {
  width: 265px;
  height: 265px;
  border: 1px solid @border-color-base1;
}
.tips {
  padding: 8px 0;
  color: @text-color-label;
}
</style>
