<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="state.title" @ok="handleSubmit" :minHeight="52" :closeFunc="onClose">
    <a-form
      :colon="false"
      :labelCol="{ style: { width: dataForm.candidateList.length || branchList.length ? '130px' : '80px' } }"
      :model="dataForm"
      ref="formElRef">
      <template v-if="eventType === 'freeApprover'">
        <a-form-item label="加签人员" name="freeApproverUserId" :rules="[{ required: true, message: `加签人员不能为空`, trigger: 'change' }]">
          <jnpf-user-select v-model:value="dataForm.freeApproverUserId" placeholder="请选择加签人员" allowClear />
        </a-form-item>
        <a-form-item label="加签类型" name="freeApproverType">
          <a-radio-group v-model:value="dataForm.freeApproverType" button-style="solid">
            <a-radio-button :value="1">审批前</a-radio-button>
            <a-radio-button :value="2">审批后</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </template>
      <template v-if="eventType === 'audit' || (eventType === 'freeApprover' && dataForm.freeApproverType === 2)">
        <a-form-item
          label="分支选择"
          name="branchList"
          :rules="[{ required: true, message: `分支不能为空`, trigger: 'change', type: 'array' }]"
          v-if="branchList.length">
          <jnpf-select
            v-model:value="dataForm.branchList"
            multiple
            placeholder="请选择审批分支"
            allowClear
            @change="onBranchChange"
            :options="branchList"
            showSearch />
        </a-form-item>
        <a-form-item
          :label="item.nodeName + item.label"
          :name="['candidateList', i, 'value']"
          v-for="(item, i) in dataForm.candidateList"
          :key="i"
          :rules="item.rules">
          <candidate-user-select
            v-model:value="item.value"
            multiple
            :placeholder="'请选择' + item.label"
            :taskId="state.taskId"
            :formData="state.formData"
            :nodeId="item.nodeId"
            v-if="item.hasCandidates" />
          <jnpf-user-select v-model:value="item.value" multiple :placeholder="'请选择' + item.label" modalTitle="候选人员" v-else />
        </a-form-item>
      </template>
      <template v-if="eventType === 'reject' && properties.rejectType && showReject">
        <a-form-item label="退回节点" name="rejectStep" :rules="[{ required: true, message: `退回节点为空`, trigger: 'change' }]">
          <jnpf-select
            v-model:value="dataForm.rejectStep"
            placeholder="请选择退回节点"
            :options="state.rejectList"
            :disabled="properties.rejectStep !== '2'"
            showSearch />
        </a-form-item>
        <a-form-item label=" " name="rejectType" v-if="properties.rejectType == 3">
          <a-radio-group v-model:value="dataForm.rejectType">
            <a-radio :value="1">重新审批<BasicHelp text="若流程为A->B->C，C退回至A，则C->A->B->C" /></a-radio>
            <a-radio :value="2">直接提交给我<BasicHelp text="若流程为A->B->C，C退回至A，则C->A->C" /></a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
      <a-form-item label="抄送人员" name="copyIds" v-if="eventType !== 'freeApprover' && properties.isCustomCopy">
        <jnpf-user-select v-model:value="dataForm.copyIds" multiple allowClear />
      </a-form-item>
      <template v-if="properties.hasOpinion">
        <a-form-item :label="`${label}意见`" name="handleOpinion">
          <jnpf-textarea v-model:value="dataForm.handleOpinion" placeholder="请输入审批意见" />
          <CommonWordsPopover ref="commonWordsPopoverRef" @confirm="insertOpinion" />
        </a-form-item>
        <a-form-item :label="`${label}附件`" name="fileList">
          <jnpf-upload-file v-model:value="dataForm.fileList" type="workFlow" :limit="3" />
        </a-form-item>
      </template>
      <a-form-item
        :label="eventType == 'freeApprover' ? '手写签名' : '审批签名'"
        name="signImg"
        v-if="properties.hasSign"
        :rules="[{ required: true, message: `请签名`, trigger: 'change' }]">
        <jnpf-sign v-model:value="dataForm.signImg" submitOnConfirm :isDefault="1" />
      </a-form-item>
      <a-form-item label="人脸识别" name="faceSignId" v-if="properties.hasFace" :rules="[{ required: true, message: `请进行人脸识别`, trigger: 'change' }]">
        <div>
          <Image v-if="dataForm.faceSignFileId" :width="80" :src="fileSaveApi.getFileLocal(dataForm.faceSignFileId)" :preview="false" class="cursor-pointer" @click="handlePreview(fileSaveApi.getFileLocal(dataForm.faceSignFileId))" />

          <a-popover v-model:visible="visible" title="人脸识别" trigger="click">
            <template #content>
              <div class="container">
                <canvas id="qrcode" ref="qrCodeRef"></canvas>
                <p class="tips">使用数智协同App扫一扫</p>
                <p class="tips">在App上人脸识别签到成功后，点击下方确认按钮</p>
                <a-button @click="handleQueryFaceSignResult">确认人脸识别成功</a-button>
              </div>
            </template>
            <a-button>APP扫码进行人脸识别</a-button>
          </a-popover>
        </div>
      </a-form-item>
    </a-form>
  </BasicModal>
</template>
<script lang="ts" setup>
import { reactive, ref, toRefs, unref, nextTick, computed, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { FormInstance, Image } from 'ant-design-vue';
  import CandidateUserSelect from './CandidateUserSelect.vue';
  import { useUserStore } from '/@/store/modules/user';
  import CommonWordsPopover from './CommonWordsPopover.vue';
import { fileSaveApi, zzUserSignFaceApi } from "/@/api";
  import { useMessage } from "/@/hooks/web/useMessage";
  import { toCanvas } from "qrcode";
  import { buildUUID } from "/@/utils/uuid";
import { createImgPreview } from "/@/components/Preview";

  interface State {
    dataForm: any;
    defaultCandidateList: any[];
    branchList: any[];
    taskId: string;
    formData: any;
    eventType: string;
    showReject: boolean;
    properties: any;
    rejectList: any[];
    title: string;
    label: string;
    faceSignBuzzId: string;
  }

  const emit = defineEmits(['register', 'confirm']);
  const userStore = useUserStore();
  const formElRef = ref<FormInstance>();
  const visible = ref<boolean>(false);
  const qrCodeRef = ref();
  const commonWordsPopoverRef = ref();
  const state = reactive<State>({
    dataForm: {
      copyIds: [],
      branchList: [],
      candidateList: [],
      fileList: [],
      handleOpinion: '',
      signImg: '',
      faceSignId: '',
      faceSignFileId: '',
      rejectType: 1,
      rejectStep: '',
      // 加签使用
      freeApproverUserId: '',
      freeApproverType: 1,
    },
    defaultCandidateList: [],
    branchList: [],
    taskId: '',
    formData: {},
    eventType: '',
    showReject: false,
    properties: {},
    rejectList: [],
    title: '',
    label: '',
    faceSignBuzzId: '',
  });
  const { dataForm, branchList, properties, showReject, eventType, label } = toRefs(state);
  const [registerModal, { changeOkLoading }] = useModalInner(init);
  const { createMessage } = useMessage();

  const getUserInfo = computed(() => userStore.getUserInfo || {});

  function init(data) {
    changeOkLoading(false);
    state.dataForm = {
      copyIds: [],
      branchList: [],
      candidateList: [],
      fileList: [],
      handleOpinion: '',
      signImg: '',
      faceSignId: '',
      faceSignFileId: '',
      rejectType: 1,
      rejectStep: '',
      freeApproverUserId: '',
      freeApproverType: 1,
    };
    state.branchList = data.branchList.map(o => ({ id: o.nodeId, fullName: o.nodeName, isCandidates: o.isCandidates, hasCandidates: o.hasCandidates }));
    state.taskId = data.taskId || '';
    state.formData = data.formData;
    state.eventType = data.eventType;
    state.title = state.eventType === 'freeApprover' ? '加签' : state.eventType === 'audit' ? '审批通过' : '审批退回';
    state.label = state.eventType === 'freeApprover' ? '加签' : '审批';
    state.showReject = data.showReject;
    state.properties = data.properties;
    state.rejectList = data.rejectList.map(o => ({ id: o.nodeCode, fullName: o.nodeName }));
    if (state.properties.hasSign) state.dataForm.signImg = unref(getUserInfo).signImg;
    state.dataForm.rejectStep = data.rejectList.length ? data.rejectList[0].nodeCode : '';
    state.defaultCandidateList = data.candidateList.map(o => ({
      ...o,
      label: '审批人',
      value: [],
      rules: [{ required: true, message: `审批人不能为空`, trigger: 'change', type: 'array' }],
    }));
    state.dataForm.candidateList = state.defaultCandidateList;
    state.faceSignBuzzId = buildUUID()
    nextTick(() => {
      formElRef.value?.clearValidate();
    });
  }
  function insertOpinion(val) {
    state.dataForm.handleOpinion += val;
  }
  function onBranchChange(val) {
    if (!val.length) return (state.dataForm.candidateList = state.defaultCandidateList);
    let list: any[] = [];
    for (let i = 0; i < val.length; i++) {
      inner: for (let j = 0; j < state.branchList.length; j++) {
        let o = state.branchList[j];
        if (val[i] === o.id && o.isCandidates) {
          list.push({
            nodeId: o.id,
            nodeName: o.fullName,
            isCandidates: o.isCandidates,
            hasCandidates: o.hasCandidates,
            label: '审批人',
            value: [],
            rules: [{ required: true, message: `审批人不能为空`, trigger: 'change', type: 'array' }],
          });
          break inner;
        }
      }
    }
    state.dataForm.candidateList = [...state.defaultCandidateList, ...list];
  }
  async function handleSubmit() {
    try {
      const values = await formElRef.value?.validate();
      if (!values) return;
      let candidateList = {};
      if (state.dataForm.candidateList.length) {
        for (let i = 0; i < state.dataForm.candidateList.length; i++) {
          candidateList[state.dataForm.candidateList[i].nodeId] = state.dataForm.candidateList[i].value;
        }
      }
      changeOkLoading(true);
      const data = {
        ...state.dataForm,
        candidateList,
        copyIds: state.dataForm.copyIds.join(','),
      };
      if (state.properties.rejectType != 3) data.rejectType = state.properties.rejectType;
      if (state.eventType !== 'freeApprover') {
        delete data.freeApproverType;
        delete data.freeApproverUserId;
      }
      emit('confirm', data);
    } catch (_) {}
  }
  async function onClose() {
    unref(commonWordsPopoverRef)?.closePopover();
    return true;
  }

  function handleQueryFaceSignResult() {
    zzUserSignFaceApi.list({
      buzzType: 'flow',
      buzzId: state.faceSignBuzzId
    }).then(res => {
      if (res.data && res.data[0]) {
        const data = res.data[0]
        state.dataForm.faceSignId = data.id
        state.dataForm.faceSignFileId = data.facePhotoId
        visible.value = false
        createMessage.success("人脸识别成功")
      } else {
        createMessage.error("未找到人脸识别记录，请确认")
      }
    })
  }

  function getQrcode() {
    const qrcode = JSON.stringify({t: 'faceSign', buzzType: 'flow', buzzId: state.faceSignBuzzId})
    toCanvas(qrCodeRef.value, qrcode, {
      margin: 0,
      width: 265,
    });
  }

  function handlePreview(img) {
    createImgPreview({ imageList: [img] });
  }

  watch(() => visible.value, val => {
    setTimeout(() => {
      getQrcode();
    }, 300)
  })

</script>
