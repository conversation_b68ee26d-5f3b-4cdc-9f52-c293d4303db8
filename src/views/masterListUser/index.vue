<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-search-box">
        <BasicForm
          @register="registerSearchForm"
          :schemas="searchSchemas"
          @advanced-change="redoHeight"
          @submit="handleSearchSubmit"
          @reset="handleSearchReset"
          class="search-form">
        </BasicForm>
      </div>
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable" v-bind="getTableBindValue" ref="tableRef" @columns-change="handleColumnChange">
          <template #tableTitle>
            <a-button type="link" @click="handelBatchRemove()"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>批量撤场</a-button>
            <a-button type="link" @click="userInfoUpdate"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('人员信息更新') }}</a-button>
            <a-button type="link" @click="removeImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('撤场导入') }}</a-button>
<!--            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('导入') }}</a-button>-->
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
          </template>
          <template #toolbar>
            <a-tooltip placement="top">
              <template #title>
                <span>{{ t('common.superQuery') }}</span>
              </template>
              <filter-outlined @click="openSuperQuery(true, { columnOptions: superQueryJson })" />
            </a-tooltip>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'entryMark'">
              <div :style="{ color: record.entryMark === 1 ? 'red' : 'green' }">{{ record.entryMark === 1 ? '入场中' : '已入场' }}</div>
            </template>

            <template v-for="(item, index) in childColumnList" v-if="childColumnList.length">
              <template v-if="column?.id?.includes('-') && item.children && item.children[0] && column.key === item.children[0]?.dataIndex">
                <ChildTableColumn
                  :data="record[item.prop]"
                  :head="item.children"
                  @toggleExpand="toggleExpand(record, item.prop + `Expand`)"
                  @toDetail="toDetail"
                  :expand="record[item.prop + `Expand`]"
                  :key="index" />
              </template>
            </template>
            <template v-if="column.jnpfKey === 'relationForm'">
              <p class="link-text" @click="toDetail(column.modelId, record[column.dataIndex + `_id`])"> {{ record[column.dataIndex] }}</p>
            </template>
            <template v-if="column.jnpfKey === 'inputNumber'">
              <jnpf-input-number v-model:value="record[column.prop]" :precision="column.precision" :thousands="column.thousands" disabled detailed />
            </template>
            <template v-if="column.jnpfKey === 'calculate'">
              <jnpf-calculate
                v-model:value="record[column.prop]"
                :isStorage="column.isStorage"
                :precision="column.precision"
                :thousands="column.thousands"
                detailed />
            </template>
            <template v-if="column.key === 'action' && !record.top">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form ref="formRef" @reload="reload" />
    <Detail ref="detailRef" />
    <ExportModal @register="registerExportModal" @download="handleDownload" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <RelationDetail ref="relationDetailRef" />
    <SuperQueryModal @register="registerSuperQueryModal" @superQuery="handleSuperQuery" />
    <UserInfoImportModal @register="registerUserInfoImportModal" @reload="reload" />
    <RemoveImport @register="registerRemoveImportModal" @reload="reload" />
    <UploadNineFile @register="registerUploadNineFile" />
    <ExitCommitment @register="registerExitCommitment" />
  </div>
</template>

<script lang="ts" setup>
  import UserInfoImportModal from './userInfoImportModal.vue';
  import { getList, del, exportData, batchDelete, exportExcel } from './helper/api';
  import { getConfigData } from '/@/api/onlineDev/visualDev';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';
  import { getDataInterfaceRes } from '/@/api/systemData/dataInterface';
  import { ref, reactive, onMounted, toRefs, computed, unref, nextTick, toRaw } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicTable, useTable, TableAction, ActionItem, TableActionType } from '/@/components/Table';
  import { SuperQueryModal } from '/@/components/CommonModal';
  import UploadNineFile from './UploadNineFile.vue';
  import ExitCommitment from './ExitCommitmentModal.vue';
  import Form from './Form.vue';
  import Detail from './Detail.vue';
  // 有关联表单详情：开始
  import RelationDetail from '/@/views/common/dynamicModel/list/detail/index.vue';
  // 有关联表单详情：结束
  import ChildTableColumn from '/@/views/common/dynamicModel/list/ChildTableColumn.vue';
  import { ExportModal } from '/@/components/CommonModal';
  import { downloadByUrl } from '/@/utils/file/download';
  import { ImportModal } from '/@/components/CommonModal';
  import { useRoute } from 'vue-router';
  import { FilterOutlined } from '@ant-design/icons-vue';
  import { getSearchFormSchemas } from '/@/components/FormGenerator/src/helper/transform';
  import { cloneDeep } from 'lodash-es';
  import columnList from './helper/columnList';
  import searchList from './helper/searchList';
  import superQueryJson from './helper/superQueryJson';
  import { dyOptionsList } from '/@/components/FormGenerator/src/helper/config';
  import RemoveImport from './RemoveImport.vue';

  interface State {
    formFlowId: string;
    flowList: any[];
    config: any;
    columnList: any[];
    printListOptions: any[];
    columnBtnsList: any[];
    customBtnsList: any[];
    treeFieldNames: any;
    leftTreeData: any[];
    leftTreeLoading: boolean;
    treeActiveId: string;
    treeActiveNodePath: any;
    columns: any[];
    complexColumns: any[];
    childColumnList: any[];
    exportList: any[];
    cacheList: any[];
    currFlow: any;
    isCustomCopy: boolean;
    candidateType: number;
    currRow: any;
    workFlowFormData: any;
    expandObj: any;
    columnSettingList: any[];
    searchSchemas: any[];
    treeRelationObj: any;
    treeQueryJson: any;
  }

  const route = useRoute();
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();

  const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerSuperQueryModal, { openModal: openSuperQuery }] = useModal();
  const [registerUserInfoImportModal, { openModal: openUserInfoImportModal }] = useModal();
  const [registerRemoveImportModal, { openModal: removeImportModal }] = useModal();
  const [registerUploadNineFile, { openModal: openUploadNineFile }] = useModal();
  const [registerExitCommitment, { openModal: openExitCommitment }] = useModal();
  const formRef = ref<any>(null);
  const tableRef = ref<Nullable<TableActionType>>(null);
  const detailRef = ref<any>(null);
  const relationDetailRef = ref<any>(null);
  const defaultSearchInfo = {
    menuId: route.meta.modelId as string,
    moduleId: '570589747513171909',
    superQueryJson: '',
    dataType: 0,
  };
  const searchInfo = reactive({
    ...cloneDeep(defaultSearchInfo),
  });
  const state = reactive<State>({
    formFlowId: '',
    flowList: [],
    config: {},
    columnList: [],
    printListOptions: [],
    columnBtnsList: [],
    customBtnsList: [],
    treeFieldNames: {
      children: 'children',
      title: 'fullName',
      key: 'id',
      isLeaf: 'isLeaf',
    },
    leftTreeData: [],
    leftTreeLoading: false,
    treeActiveId: '',
    treeActiveNodePath: [],
    columns: [],
    complexColumns: [], // 复杂表头
    childColumnList: [],
    exportList: [],
    cacheList: [],
    currFlow: {},
    isCustomCopy: false,
    candidateType: 1,
    currRow: {},
    workFlowFormData: {},
    expandObj: {},
    columnSettingList: [],
    searchSchemas: [],
    treeRelationObj: null,
    treeQueryJson: {},
  });
  const selList = ref<any[]>([]);
  const { flowList, childColumnList, searchSchemas } = toRefs(state);
  const [registerSearchForm, { updateSchema, resetFields, submit: searchFormSubmit }] = useForm({
    baseColProps: { span: 6 },
    showActionButtonGroup: true,
    showAdvancedButton: true,
    compact: true,
  });
  const [registerTable, { reload, setLoading, getFetchParams, getSelectRowKeys, redoHeight, clearSelectedRowKeys }] = useTable({
    api: getList,
    immediate: false,
    clickToRowSelect: true, // 点击选中条目
    ellipsis: false,
    rowSelection: {
      // 选中行
      onChange: (selectedRowKeys, selectedRows) => {
        // 选中行change
        console.log('selectedRowKeys', selectedRowKeys);
        selList.value = selectedRowKeys;
      },
    },
    afterFetch: data => {
      const list = data.map(o => ({
        ...o,
        ...state.expandObj,
      }));
      state.cacheList = cloneDeep(list);
      return list;
    },
  });

  const getTableBindValue = computed(() => {
    let columns = state.complexColumns;
    const data: any = {
      pagination: { pageSize: 20 }, //有分页
      searchInfo: unref(searchInfo),
      defSort: {
        sort: 'desc', //sort
        sidx: '', //取defaultSidx
      },
      columns,
      rowSelection: {
        type: 'checkbox',
        getCheckboxProps: record => ({ disabled: !!record.top }),
      },
      bordered: true,
      actionColumn: {
        width: 270,
        title: '操作',
        dataIndex: 'action',
      },
    };
    return data;
  });

  function init() {
    state.config = {};
    searchInfo.menuId = route.meta.modelId as string;
    state.columnList = columnList;
    setLoading(true);
    getSearchSchemas();
    getColumnList();
    nextTick(() => {
      // 有搜索列表
      searchFormSubmit();
    });
  }

  function getSearchSchemas() {
    const schemas = getSearchFormSchemas(searchList);
    state.searchSchemas = schemas;
    schemas.forEach(cur => {
      const config = cur.__config__;
      if (dyOptionsList.includes(config.jnpfKey)) {
        if (config.dataType === 'dictionary') {
          if (!config.dictionaryType) return;
          getDictionaryDataSelector(config.dictionaryType).then(res => {
            updateSchema([{ field: cur.field, componentProps: { options: res.data.list } }]);
          });
        }
        if (config.dataType === 'dynamic') {
          if (!config.propsUrl) return;
          const query = { paramList: config.templateJson || [] };
          getDataInterfaceRes(config.propsUrl, query).then(res => {
            const data = Array.isArray(res.data) ? res.data : [];
            updateSchema([{ field: cur.field, componentProps: { options: data } }]);
          });
        }
      }
      cur.defaultValue = cur.value;
    });
  }

  function getColumnList() {
    // 没有开启列表权限
    let columnList = state.columnList;
    state.exportList = columnList;
    let columns = columnList.map(o => ({
      ...o,
      title: o.label,
      dataIndex: o.prop,
      align: o.align,
      fixed: o.fixed == 'none' ? false : o.fixed,
      sorter: o.sortable,
      width: o.width || 100,
    }));
    //添加复杂表头
    columns = getComplexColumns(columns);
    state.columns = columns.filter(o => o.prop.indexOf('-') < 0);
    //子表表头
    getChildComplexColumns(columns);
  }

  //复杂表头
  function getComplexColumns(columns) {
    //这里生成复杂表头的配置
    let complexHeaderList: any[] = [];
    if (!complexHeaderList.length) return columns;
    let childColumns: any[] = [];
    for (let i = 0; i < complexHeaderList.length; i++) {
      const e = complexHeaderList[i];
      e.title = e.fullName;
      e.align = e.align;
      e.dataIndex = e.id;
      e.prop = e.id;
      e.children = [];
      e.jnpfKey = 'complexHeader';
      if (e.childColumns?.length) {
        childColumns.push(...e.childColumns);
        for (let k = 0; k < e.childColumns.length; k++) {
          const item = e.childColumns[k];
          for (let j = 0; j < columns.length; j++) {
            const o = columns[j];
            if (o.__vModel__ == item && o.fixed !== 'left' && o.fixed !== 'right' && !o.__config__.isSubTable) e.children.push({ ...o });
          }
        }
      }
    }
    complexHeaderList = complexHeaderList.filter(o => o.children.length);
    for (let i = 0; i < columns.length; i++) {
      const item = columns[i];
      if (!childColumns.includes(item.__vModel__) || item.__config__.isSubTable) complexHeaderList.push(item);
    }
    return complexHeaderList;
  }

  //子表表头
  function getChildComplexColumns(columnList) {
    let list: any[] = [];
    for (let i = 0; i < columnList.length; i++) {
      const e = columnList[i];
      if (!e.prop.includes('-')) {
        list.push(e);
      } else {
        let prop = e.prop.split('-')[0];
        let vModel = e.prop.split('-')[1];
        let label = e.label.split('-')[0];
        let childLabel = e.label.replace(label + '-', '');
        let newItem = {
          align: 'center',
          jnpfKey: 'table',
          prop,
          label,
          title: label,
          dataIndex: prop,
          children: [],
        };
        e.dataIndex = vModel;
        e.title = childLabel;
        if (!state.expandObj.hasOwnProperty(prop + `Expand`)) state.expandObj[prop + `Expand`] = false;
        if (!list.some(o => o.prop === prop)) list.push(newItem);
        for (let i = 0; i < list.length; i++) {
          if (list[i].prop === prop) {
            list[i].children.push(e);
            break;
          }
        }
      }
    }
    // 行内分组展示
    getMergeList(list);

    state.complexColumns = list;
    state.childColumnList = list.filter(o => o.jnpfKey === 'table');

    // 子表分组展示宽度取100
    for (let i = 0; i < state.childColumnList.length; i++) {
      const e = state.childColumnList[i];
      if (e.children?.length) e.children = e.children.map(o => ({ ...o, width: 100 }));
    }
  }

  function getMergeList(list) {
    list.forEach(item => {
      if (item.jnpfKey === 'table' && item.children && item.children.length) {
        item.children.forEach((child, index) => {
          if (index == 0) {
            child.customCell = () => ({
              rowspan: 1,
              colspan: item.children.length,
              class: 'child-table-box',
            });
          } else {
            child.customCell = () => ({
              rowspan: 0,
              colspan: 0,
            });
          }
        });
      }
    });
  }

  function toggleExpand(row, field) {
    row[field] = !row[field];
  }

  // 关联表单查看详情
  function toDetail(modelId, id) {
    if (!id) return;
    getConfigData(modelId).then(res => {
      if (!res.data || !res.data.formData) return;
      const formConf = JSON.parse(res.data.formData);
      formConf.popupType = 'general';
      const data = { id, formConf, modelId };
      relationDetailRef.value?.init(data);
    });
  }

  function handleColumnChange(data) {
    state.columnSettingList = data;
  }

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: updateHandle.bind(null, record),
      },
      {
        label: '退场承诺书',
        onClick: editExitCommitment.bind(null, record.id),
      },
      {
        label: '撤场',
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
      {
        label: '查看资料',
        onClick: uploadFile.bind(null, record.id),
        // modelConfirm: {
        //   onOk: uploadFile.bind(null, record),
        // },
      },
      {
        label: '详情',
        onClick: goDetail.bind(null, record),
      },
    ];
  }

  // 查看资料
  function uploadFile(id) {
    console.log('---------------', id);
    openUploadNineFile(true, id);
  }

  // 编辑
  function updateHandle(record) {
    // 不带工作流
    const data = {
      id: record.id,
      menuId: searchInfo.menuId,
      allList: state.cacheList,
    };
    formRef.value?.init(data);
  }

  // 删除
  function handleDelete(id) {
    del(id).then(res => {
      createMessage.success(res.msg);
      clearSelectedRowKeys();
      reload();
    });
  }

  // 查看详情
  function goDetail(record) {
    // 不带流程
    const data = {
      id: record.id,
    };
    detailRef.value?.init(data);
  }

  // 新增
  function addHandle() {
    // 不带流程新增
    const data = {
      id: '',
      menuId: searchInfo.menuId,
      allList: state.cacheList,
    };
    formRef.value?.init(data);
  }

  function editExitCommitment(id){
    console.log('editExitCommitment')
    openExitCommitment(true, id);
  }

  // 导出
  function handleDownload(data) {
    let query = { ...getFetchParams(), ...data };
    exportData(query)
      .then(res => {
        setExportModalProps({ confirmLoading: false });
        if (!res.data.url) return;
        downloadByUrl({ url: res.data.url });
        closeExportModal();
      })
      .catch(() => {
        setExportModalProps({ confirmLoading: false });
      });
  }

  // 批量删除
  function handelBatchRemove() {
    const ids = getSelectRowKeys();
    if (!ids.length) return createMessage.error('请选择一条数据');
    createConfirm({
      iconType: 'warning',
      title: t('common.tipTitle'),
      content: '您确定要批量撤场吗, 是否继续?',
      onOk: () => {
        batchDelete(ids).then(res => {
          createMessage.success(res.msg);
          clearSelectedRowKeys();
          reload();
        });
      },
    });
  }

  // 高级查询
  function handleSuperQuery(superQueryJson) {
    searchInfo.superQueryJson = superQueryJson;
    reload({ page: 1 });
  }

  function handleSearchReset() {
    searchFormSubmit();
  }

  function handleSearchSubmit(data) {
    clearSelectedRowKeys();
    let obj = {
      ...defaultSearchInfo,
      superQueryJson: searchInfo.superQueryJson,
      ...data,
      ...(state.treeQueryJson || {}),
    };
    Object.keys(searchInfo).map(key => {
      delete searchInfo[key];
    });
    for (let [key, value] of Object.entries(obj)) {
      searchInfo[key.replaceAll('-', '_')] = value;
    }
    console.log(searchInfo);
    reload({ page: 1 });
  }

  function userInfoUpdate() {
    openUserInfoImportModal(true, {});
  }

  function removeImport() {
    removeImportModal(true, {});
  }

  function handleImport() {
    openImportModal(true);
  }

  function handleExport() {
    setLoading(true);
    exportExcel({
      'id#$in': selList.value,
      _dataType: 0,
      _sorter: 'f_creator_time Asc',
    })
      .then(_res => {
        // setLoading(true);
        // if (!res.data.url) return;
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }

  onMounted(() => {
    init();
  });
</script>
