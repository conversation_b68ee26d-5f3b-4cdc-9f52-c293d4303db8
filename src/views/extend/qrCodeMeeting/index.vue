<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button @click="addOrUpdateHandle()" type="primary" preIcon="icon-ym icon-ym-btn-add">新建</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload"/>
  </div>
</template>

<script setup lang="ts">
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from "/@/components/Table";
import { zzQrCodeTableApi as api } from '/@/api';
import { usePopup } from "/@/components/Popup";
import { useMessage } from "/@/hooks/web/useMessage";
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import Form from "./Form.vue";


defineOptions({ name: 'extend-tableDemo-baseTable' });

const {createMessage} = useMessage();
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const columns: BasicColumn[] = [
  { title: '会议标题', dataIndex: 'meetingTitle', width: undefined },
  { title: '会议创建时间', dataIndex: 'mettingStartTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '会议结束时间', dataIndex: 'mettingEndTime', width: 170, format: 'date|YYYY-MM-DD HH:mm:ss' },
  { title: '会议状态', dataIndex: 'status', width: undefined },
];
const [registerTable, { reload }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('状态', 'status'),
    ],
  },
  actionColumn: {
    width: 90,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

function addOrUpdateHandle(id='') {
  api.page
  openFormPopup(true, {id});
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
