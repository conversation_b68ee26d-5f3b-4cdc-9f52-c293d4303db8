<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <a-tabs type="card">
          <a-tab-pane key="1" tab="待分配">
            <noSpecifiy />
          </a-tab-pane>
          <a-tab-pane key="2" tab="已分配">
            <specifiy />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import noSpecifiy from './noSpecifiy.vue'
import specifiy from './specifiy.vue'
</script>