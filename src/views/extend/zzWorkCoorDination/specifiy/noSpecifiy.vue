<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <!-- <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button> -->
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'selectPerNames'">
              <span>{{record.selectPerNames.join(',')}}</span>
            </template>
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <FaFlowCube ref="flowRef" flow-en-code="zzWorkCoordination" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import { zzWorkCoordinationApi as api } from '/@/api';
import { genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn, genQueryInput } from "/@/utils/tableUtils";
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { FLOW_OP_TYPE } from "/@/enums/zzEnums";


defineOptions({name: 'extend-zzWorkCoorDination'});

const flowRef = ref<any>();
const { createMessage } = useMessage();
const columns: BasicColumn[] = [
  {title: '问题单编号', dataIndex: 'questionNo', width: 150},
  {title: '外部编号', dataIndex: 'questionName', width: 150},
  {title: '机组', dataIndex: 'crew', width: 80},
  {title: '厂房', dataIndex: 'workshop',width: 80},
  {title: '区域', dataIndex: 'area', width: 80},
  {title: '系统', dataIndex: 'systemName'},
  {title: '专业', dataIndex: 'expertise'},
  {title: '提出问题', dataIndex: 'submitQuestion'},
  {title: '问题类型', dataIndex: 'questionType'},
  {title: '编制部门', dataIndex: 'deptName'},
  {title: '编制日期', dataIndex: 'creatorTime', format: 'date|YYYY-MM-DD'},
  {title: '手机号', dataIndex: 'phone'},
  {title: '需要答复日期', dataIndex: 'needResDate', width: 120, format: 'date|YYYY-MM-DD'},
  {title: '影响', dataIndex: 'effect', width: 180},
  {title: '涉及专业', dataIndex: 'effectExpertise', width: 180},
  {title: '问题内容', dataIndex: 'questionContext'}, 
  {title: '班组建议', dataIndex: 'teamAdvice'},
  {title: '技术员', dataIndex: 'techPerson'},
  // 流程字段
  {title: '审批状态', dataIndex: 'currentState', width: 100},
  {title: '制单人员', dataIndex: 'creatorUser', width: 130},
  {title: '发起时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm'},
];
const [registerTable, {reload}] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQueryInput('问题单编号', 'questionNo'),
    ],
  },
  searchInfo: {
    //待提交只查询本人和状态属于0的数据
    //已提交，本人，状态不属于0的数据
    //待分配， 状态属于1的数据
    //已分配， 状态值>1 的数据
    //待处理， 状态属于2的数据
    //已处理， 状态值>2 的数据
    //待处理， 状态属于3的数据
    //已处理， 状态值>3 的数据
    //待验证， 状态属于4的数据
    //已验证， 状态值>4 的数据
    //全部， 状态值>=1 的数据
    'currentLink#$equal': 1,
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
  actionColumn: { width: 120, title: '操作', dataIndex: 'action' },
});

function getTableActions(record): ActionItem[] {
  return [
    // genFlowEditBtn(record, toDetail),
    // genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
  ];
}

function handleAdd() {
  flowRef.value.handleAdd()
}

function toDetail(record:any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType)
}

function handleDelete(id:string) {
  api.flowRemove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
