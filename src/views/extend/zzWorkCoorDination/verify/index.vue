<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <a-tabs type="card">

          <a-tab-pane key="2" tab="待验证">
            <verify />
          </a-tab-pane>
          <a-tab-pane key="2" tab="已验证">
            <noVerify />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

import verify from './verify.vue'
import noVerify from './noVerify.vue'
</script>