<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <a-tabs type="card">
          <a-tab-pane key="1" tab="待处理">
            <disposal />
          </a-tab-pane>
          <a-tab-pane key="2" tab="已处理">
            <noDisposal />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import disposal from './disposal.vue'
import noDisposal from './noDisposal.vue'
</script>