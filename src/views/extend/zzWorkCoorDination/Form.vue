<template>
  <div class="flow-form">
    <div class="flow-com-title">
      <h1>施工协调</h1>
    </div>
    <a-form :colon="false" :labelCol="{ style: { width: '100px' } }" :model="dataForm" :rules="dataRule" ref="formRef" :disabled="config.disabled">
      <a-row>
        <a-col :span="12" v-if="judgeShow('questionNo')">
          <a-form-item label="问题单编号" name="questionNo">
            <a-input v-model:value="dataForm.questionNo" placeholder="问题单编号" :disabled="judgeWrite('questionNo')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('planDate')">
          <a-form-item label="外部编号" name="questionName">
            <a-input v-model:value="dataForm.questionName" placeholder="外部编号" :disabled="judgeWrite('questionName')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('crew')">
          <a-form-item label="机组" name="crew">
            <jnpf-select v-model:value="dataForm.crew" :options="crewSelect" placeholder="机组" :disabled="judgeWrite('crew')" />
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="judgeShow('workshop')">
          <a-form-item label="厂房" name="workshop">
            <jnpf-select v-model:value="dataForm.workshop"  :options="workshopSelect" placeholder="厂房" :disabled="judgeWrite('workshop')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('area')">
          <a-form-item label="区域" name="area">
            <jnpf-select v-model:value="dataForm.area" :options="areaSelect" placeholder="区域" :disabled="judgeWrite('area')" />
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="judgeShow('systemName')">
          <a-form-item label="系统" name="systemName">
            <jnpf-select v-model:value="dataForm.systemName" :options="systemNameSelect" showSearch placeholder="系统" :disabled="judgeWrite('systemName')" />
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="judgeShow('expertise')">
          <a-form-item label="专业" name="expertise">
            <jnpf-selectt v-model:value="dataForm.expertise" :options="expertiseSelect" placeholder="专业" :disabled="judgeWrite('expertise')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" />
        <a-col :span="12" v-if="judgeShow('submitQuestion')">
          <a-form-item label="提出问题" name="submitQuestion">
            <a-input v-model:value="dataForm.submitQuestion" placeholder="提出问题" :disabled="judgeWrite('submitQuestion')" />
          </a-form-item>
        </a-col>
       
        <a-col :span="12" v-if="judgeShow('questionType')">
          <a-form-item label="问题类型" name="questionType">
            <jnpf-select v-model:value="dataForm.questionType" :options="questionTypeSelect" placeholder="问题类型" :disabled="judgeWrite('questionType')" />
          </a-form-item>
        </a-col>

        <a-col :span="24" v-if="judgeShow('deptName')">
          <a-form-item label="编制部门" name="deptName">
            <a-input v-model:value="dataForm.deptName" placeholder="编制部门" :disabled="judgeWrite('deptName')" />
          </a-form-item>
        </a-col>

        <a-col :span="12" v-if="judgeShow('creatorTime')">
          <a-form-item label="编制日期" name="creatorTime">
            <jnpf-date-picker v-model:value="dataForm.creatorTime" placeholder="编制日期" :disabled="judgeWrite('creatorTime')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('phone')">
          <a-form-item label="手机号" name="phone">
            <a-input v-model:value="dataForm.phone" placeholder="手机号" :disabled="judgeWrite('phone')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('needResDate')">
          <a-form-item label="需要答复日期" name="needResDate">
            <jnpf-date-picker v-model:value="dataForm.needResDate" placeholder="需要答复日期" :disabled="judgeWrite('needResDate')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('effect')">
          <a-form-item label="影响" name="effect">
            <a-input v-model:value="dataForm.effect" placeholder="影响" :disabled="judgeWrite('effect')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('effectExpertise')">
          <a-form-item label="涉及专业" name="effectExpertise">
            <jnpf-select v-model:value="dataForm.effectExpertise" :options="expertiseSelect" placeholder="涉及专业" :disabled="judgeWrite('effectExpertise')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('questionContext')">
          <a-form-item label="问题内容" name="questionContext">
            <a-input v-model:value="dataForm.questionContext" placeholder="问题内容" :disabled="judgeWrite('questionContext')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('teamAdvice')">
          <a-form-item label="班组建议" name="teamAdvice">
            <a-input v-model:value="dataForm.teamAdvice" placeholder="班组建议" :disabled="judgeWrite('teamAdvice')" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('techPersonId')">
          <a-form-item label="技术员" name="techPersonId">
            <jnpf-user-select v-model:value="dataForm.techPersonId" placeholder="技术员" :disabled="judgeWrite('techPersonId')" />
          </a-form-item>
        </a-col>




        <!-- <a-col :span="12" v-if="judgeShow('picList')">
          <a-form-item label="上传照片" name="picList">
            <jnpf-fa-upload-file-qiniu v-model:value="dataForm.picList" :disabled="judgeWrite('picList')" :multiple="true" accept=".png,.jpg" />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="judgeShow('videoList')">
          <a-form-item label="上传视频" name="videoList">
            <jnpf-fa-upload-file-qiniu v-model:value="dataForm.videoList" :disabled="judgeWrite('videoList')" :multiple="true" accept=".mp4" />
          </a-form-item>
        </a-col> -->

      </a-row>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, toRefs, watch } from 'vue';
import { useFlowForm } from '/@/views/workFlow/workFlowForm/hooks/useFlowForm';
import type { FormInstance } from 'ant-design-vue';
// import JnpfFaUploadFileQiniu from "/@/components/Jnpf/Upload/src/FaUploadFileQiniu.vue";
import { zzWorkCoordinationApi } from "/@/api";
import { isNil } from "lodash-es";
import { Rule } from "/@/components/Form";
import { useBaseStore } from '/@/store/modules/base';
const baseStore = useBaseStore();
interface State {
  dataForm: any;
  dataRule: Record<string, Rule[]>;
}

defineOptions({ name: 'zzWorkCoordination' });// defineOptions({ name: 'zzProjectMediaPlan' });
const props = defineProps(['config']);
const emit = defineEmits(['setPageLoad', 'eventReceiver']);
let crewSelect = ref([]);
let workshopSelect = ref([]);
let areaSelect = ref([]);
let systemNameSelect = ref([]);
let expertiseSelect = ref([]);
let questionTypeSelect = ref([]);
const formRef = ref<FormInstance>();
const state = reactive<State>({
  dataForm: {
    flowId: '',
    id: '',
    //字典选择项


    // 业务字段
    // questionNo: '',
    // planDate: 0,
    // workshop: '',
    // area: '',
    // systemName: '',
    // machGrpNo: '',
    // submitQuestion: '',
    // questionType: '',
    // deptName: '',
    // picNum: 0,
    // videoNum: 0,
    // picList: [],
    // videoList: [],
  },
  dataRule: {
    // taskCode: [{ required: true, message: '作业编码不能为空', trigger: 'blur' }],
    // planDate: [{ required: true, message: '拍摄窗口期不能为空', trigger: 'change' }],
    // workshop: [{ required: true, message: '厂房不能为空', trigger: 'change' }],
    // area: [{ required: true, message: '区域不能为空', trigger: 'change' }],
    // systemName: [{ required: true, message: '系统不能为空', trigger: 'change' }],
    // questionType: [{ required: true, message: '责任人不能为空', trigger: 'change' }],
  },
});
const { dataForm, dataRule } = toRefs(state);
const { init, judgeShow, judgeWrite, dataFormSubmit } = useFlowForm({
  config: props.config,
  selfState: state,
  emit,
  formRef,
});

// 区域（核岛/常规岛/BOP/其他）
const workshopOptions = [
  { id: '核岛', fullName: '核岛' },
  { id: '常规岛', fullName: '常规岛' },
  { id: 'BOP', fullName: 'BOP' },
  { id: '其他', fullName: '其他' },
]
const subjectOptions = ref([])

defineExpose({ dataFormSubmit });

/** 表单初始化信息 */
function selfInit() {
  // state.dataForm.flowTitle = unref(getUserInfo).userName + '的请假申请';
  getSubProjectList()
  getOptions()
}
// function init(){
//   getOptions()
// }
async function getSubProjectList() {
  const res = await zzWorkCoordinationApi.all()
  subjectOptions.value = res.data.map(i => ({ id: i.id, fullName: i.name }))
}

watch(() => state.dataForm.systemName, async (val) => {
  console.log('systemName', val)
  if (isNil(val)) {
    state.dataForm.machGrpNo = '';
    return;
  }
  const res = await zzWorkCoordinationApi.getById(val)
  state.dataForm.machGrpNo = res.data.crew;
})

async function getOptions() {
    const options = (await baseStore.getDictionaryData('workCoordination')) as any[];
    console.log(options);

    crewSelect = setSelectValue(options, '机组')
    workshopSelect = setSelectValue(options, '厂房')
    areaSelect = setSelectValue(options, '区域')
    systemNameSelect = setSelectValue(options, '系统')
    expertiseSelect = setSelectValue(options, '专业')
    questionTypeSelect = setSelectValue(options, '问题类型')
    console.log(areaSelect)

  }

function setSelectValue(selector, str) {
    // 遍历数组，查找匹配的元素
    for (let i = 0; i < selector.length; i++) {
      if (selector[i].fullName === str) {
        // 找到匹配的元素，将它的children属性返回
        if (selector[i].children!=undefined){
          return selector[i].children.map(i => ({ id: i.id, fullName: i.fullName }));
        }
      }
    }
    // 如果没有找到匹配的元素，返回undefined
    return undefined;
  }
onMounted(() => {
  init();
  selfInit();
  getOptions();
});
</script>
