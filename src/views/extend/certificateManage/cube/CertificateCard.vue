<template>
  <div class="fa-flex-row">
    <!-- left panel -->
    <div style="position: relative; width: 500px; background: #FFF; border: 2px solid #333;border-radius: 18px;overflow: hidden;" class="fa-flex-column fa-text-center fa-mr4">
      <div class="fa-zz-card-top-banner" style="padding: 0 16px;">
        <div class="fa-zz-card-top-banner-logo" />
        <div class="fa-flex-column-center" style="color: #0E3092; font-size: 13px; line-height: 16px; font-weight: 600; margin-top: 4px;">
          <div>中核工程</div>
          <div>CNPE</div>
        </div>
        <div class="fa-flex-1" style="color: #0E3092; font-size: 28px; letter-spacing: 8px; font-weight: 600;">漳州核电工程</div>
        <div class="fa-zz-card-top-banner-logo" />
        <div class="fa-flex-column-center" style="color: #0E3092; font-size: 13px; line-height: 16px; font-weight: 600; margin-top: 4px;">
          <div>中核五公司</div>
          <div>CNF</div>
        </div>
      </div>
      <div class="fa-zz-card-border-img" />

      <div style="display: flex; flex-direction: row;" >
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 2px 20px;">
          <img style="height: 170px; width: 120px; object-fit: cover; border: 1px solid #333;" :src="photoUrl" :alt="data.name" />
          <div style="letter-spacing: 8px; font-weight: 600; font-size: 20px;">{{ data.name }}</div>
        </div>

        <div>
          <div class="fa-flex-row-center" style="margin-bottom: 6px;">
            <div style="font-size: 22px; font-weight: 700; letter-spacing: 10px; margin-right: 20px;">监护证</div>
            <div style="font-size: 18px; font-weight: 600;">{{ data.custodyNumber }}</div>
          </div>

          <div v-for="item in typeList" :key="item.name" class="fa-flex-row-center fa-zz-card-line">
            <div style="width: 14px; height: 14px; margin-right: 12px; border-radius: 7px; border: 1px solid #333" :style="getTypeStyle(item.checkType)" />
            <div class="fa-flex-row-center fa-mr4" style="flex: 1; border-bottom: 1px dashed #333; padding-left: 12px; font-size: 16px; line-height: 20px;">
              <div style="width: 80px; text-align: left; font-weight: 600;">{{ item.name }}</div>
              <div style="text-align: left; font-weight: 600;">{{ getTypeValid(item.checkType) }}</div>
            </div>
          </div>

        </div>
      </div>
      <div class="fa-zz-card-border-img" />

      <div class="fa-zz-card-bottom-text">
        <div style="letter-spacing: 3px; font-weight: 600;">责任</div>
        <div style="letter-spacing: 3px; font-weight: 600;">安全</div>
        <div style="letter-spacing: 3px; font-weight: 600;">创新</div>
        <div style="letter-spacing: 3px; font-weight: 600;">协同</div>
      </div>

      <img src="/plugins/zz/certificate/zz_stamp.png" class="fa-zz-card-stamp" />
    </div>

    <!-- right panel -->
    <div style="width: 500px; background: #FFF;  border: 2px solid #333; border-radius: 18px;overflow: hidden; padding: 12px; justify-content: space-between; position: relative; font-weight: 600; font-size: 18px; line-height: 32px;" class="fa-flex-column fa-zz-card-right">
      <div class="fa-flex-row">
        <div style="width: 24px;">1.</div>
        <div class="fa-flex-1">监护人应确认危险作业已按照安全标准化的要求，建立好警戒区域，并防止无关人员入内。</div>
      </div>
      <div class="fa-flex-row">
        <div style="width: 24px;">2.</div>
        <div class="fa-flex-1">监护人不得从事现场作业，不得擅离职守。</div>
      </div>
      <div class="fa-flex-row">
        <div style="width: 24px;">3.</div>
        <div class="fa-flex-1">监护人应制止人员的危险作业行为。</div>
      </div>
      <div class="fa-flex-row" style="width: 320px;">
        <div style="width: 24px;">4.</div>
        <div class="fa-flex-1">此证件限本人在有效期内使用，严禁转借、复制、涂改。</div>
      </div>
      <div class="fa-flex-row" style="width: 320px;">
        <div style="width: 24px;">5.</div>
        <div class="fa-flex-1">请妥善保管，如有遗失、损坏，应及时报告证件发放部门。</div>
      </div>
      <div class="fa-flex-row">
        <div style="width: 24px;">6.</div>
        <div class="fa-flex-1">本单位应急电话:19559376095</div>
      </div>

      <canvas id="qrcode" ref="qrCodeRef" class="fa-zz-card-qrcode" style="position: absolute; right: 12px; bottom: 12px;"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { find, isNil } from 'lodash-es'
import dayjs from 'dayjs';
import { fileSaveApi } from "/@/api";
import { toCanvas } from 'qrcode';
import { PageEnum } from "/@/enums/pageEnum";

defineOptions({name: 'CertificateCard'});

const props = defineProps(['data']); // 暴露给外部传入的属性
const { data } = props
const qrCodeRef = ref();
console.log('证书数据', data)
// const photoUrl = 'http://fa.file.dward.cn/zz_szh/pro/2024-07-26/1721973897833_epii_tmp_1721973897641_20240726140459.jpg';
const photoUrl = fileSaveApi.getFileLocal(data.officialPhotoFileId);

const typeList = [
  { name: '动火作业', checkType: '动火' },
  { name: '高处作业', checkType: '高处' },
  { name: '受限空间', checkType: '受限空间' },
  { name: '起重吊装', checkType: '起重吊装' },
  { name: '脚手架', checkType: '脚手架' },
  { name: '其他', checkType: '其他' },
]

function getTypeStyle(typeName: string) {
  const typeGrade = find(data.gradeList, (item) => item.trainingName.indexOf(typeName) > -1)
  // 1. 检查考试是否合格
  let pass = typeGrade && typeGrade.trainingQualified === '1'
  // 2. 检查有效期
  try {
    if (typeGrade && typeGrade.validEnd && dayjs().isAfter(dayjs(typeGrade.validEnd))) {
      pass = false
    }
  } catch (e) {}
  return { background: pass ? '#333' : '#FFF' }
}

function getTypeValid(typeName: string) {
  const typeGrade = find(data.gradeList, (item) => item.trainingName.indexOf(typeName) > -1)
  if (isNil(typeGrade)) return "";
  let str = "";
  if (typeGrade.trainingTime) {
    str += dayjs(typeGrade.trainingTime).format('YYYY.MM.DD')
  }
  if (typeGrade.validEnd) {
    str += '-' + dayjs(typeGrade.validEnd).format('YYYY.MM.DD')
  }
  return str;
}

function getQrcode() {
  const qr = window.location.origin + PageEnum.BASE_CERTIFICATE_VIEW_LINK + "?id=" + data.id
  toCanvas(qrCodeRef.value, qr, {
    margin: 0,
    width: 140,
  });
}

onMounted(() => {
  getQrcode()
})
</script>

<style scoped>
</style>
