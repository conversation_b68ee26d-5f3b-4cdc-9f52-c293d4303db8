<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新建</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>导出</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>导入</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <ExportModal @register="registerExportModal" />
    <ImportModal @register="registerImportModal" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import { genDeleteBtn, genEditBtn, genQueryInput, genQuerySearch } from "/@/utils/tableUtils";
import { useModal } from "/@/components/Modal";
import { usePopup } from '/@/components/Popup';
import { useMessage } from '/@/hooks/web/useMessage';
import { zzProjectContractSubApi as api } from '/@/api';
import Form from './Form.vue'
import ExportModal from './ExportModal.vue';
import ImportModal from './ImportModal.vue';


defineOptions({ name: 'extend-zz-project-contract-sub' });

const { createMessage } = useMessage();
const [registerExportModal, { openModal: openExportModal }] = useModal();
const [registerImportModal, { openModal: openImportModal }] = useModal();
const columns: BasicColumn[] = [
  // { title: '序号', dataIndex: 'id', width: 80 },
  { title: '合同名称', dataIndex: 'contractName' },
  { title: '子项名称', dataIndex: 'name' },
  { title: '子项编码', dataIndex: 'no', width: 200 },
  { title: '机组', dataIndex: 'crew', width: 100 },
  // { title: '计划开工时间', dataIndex: 'planStartTime', width: 100, format: 'date|YYYY-MM-DD' },
  // { title: '计划完工时间', dataIndex: 'planEndTime', width: 100, format: 'date|YYYY-MM-DD' },
  // { title: '工期', dataIndex: 'planWorkDay', width: 60 },
  // {title: '土建劳务分包', dataIndex: 'fbTjlw'},
  // {title: '基础专业分包', dataIndex: 'fbBasicTech'},
  // {title: '装饰装修专业分包', dataIndex: 'fbDecoration'},
  // {title: '钢结构专业分包', dataIndex: 'fbSteel'},
  // {title: '安装', dataIndex: 'fbInstall'},
  // {title: '备注', dataIndex: 'note'},
];
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerTable, { reload, getFetchParams }] = useTable({
  api: api.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('合同名称', 'contractName'),
      genQueryInput('子项名称', 'name'),
      genQueryInput('子项编码', 'no'),
      genQueryInput('机组', 'crew'),
    ],
  },
  actionColumn: {
    width: 90,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    genEditBtn(record, addOrUpdateHandle),
    genDeleteBtn(record, handleDelete),
  ];
}

function handleDelete(id:any) {
  api.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}

function handleExport() {
  const listQuery = {
    ...getFetchParams(),
  };
  openExportModal(true, { listQuery });
}

function handleImport() {
  openImportModal(true, {});
}
</script>
