<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px">
    </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { genInput } from "/@/utils/formUtils";
import { zzProjectContractSubApi } from '/@/api';

const id = ref('');

const schemas: FormSchema[] = [
  genInput('合同名称', 'contractName'),
  genInput('子项名称', 'name'),
  genInput('子项编码', 'no'),
  genInput('机组', 'crew'),
  // genDate('计划开工时间', 'planStartTime'),
  // genDate('计划完工时间', 'planEndTime'),
  // genInput('土建劳务分包', 'fbTjlw', false),
  // genInput('基础专业分包', 'fbBasicTech', false),
  // genInput('装饰装修专业分包', 'fbDecoration', false),
  // genInput('钢结构专业分包', 'fbSteel', false),
  // genInput('安装', 'fbInstall', false),
  // genInput('备注', 'note', false),
];
const getTitle = computed(() => (!unref(id) ? '新建工程合同子项' : '编辑工程合同子项'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields}] = useForm({labelWidth: 120, schemas: schemas});
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

async function init(data:any) {
  resetFields();
  id.value = data.id;
  if (id.value) {
    changeLoading(true);
    zzProjectContractSubApi.getById(id.value).then(res => {
      setFieldsValue(res.data);
      changeLoading(false);
    });
  }
}

function handleFieldValueChange(field:any, value:any) {
  console.log('field', field, 'value', value);
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const data = {
    ...values,
    id: id.value,
  };
  const formMethod = id.value ? zzProjectContractSubApi.update : zzProjectContractSubApi.save;
  formMethod(data)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closePopup();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
