<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDownload()" :disabled="selList.length === 0">批量下载照片</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'issuePicList'">
              <div style="width: 120px;">
                <a-image v-if="Array.isArray(record.issuePicList) && record.issuePicList.length > 0" :src="fileSaveApi.getFileLocal(record.issuePicList[0])"></a-image>
              </div>
            </template>
            <template v-if="column.key === 'handlePicList'">
              <div style="width: 120px;">
                <a-image v-if="Array.isArray(record.handlePicList) && record.handlePicList.length > 0" :src="fileSaveApi.getFileLocal(record.handlePicList[0])"></a-image>
              </div>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ActionItem, BasicTable, TableAction, useTable } from '/@/components/Table';
import {fileSaveApi, issueHandleRecordPicApi as api} from '/@/api';
import {genQueryInput} from "/@/utils/tableUtils";
import { useMessage } from "/@/hooks/web/useMessage";
import {issuePicColumn} from "/@/enums/issueEnums";


defineOptions({name: 'extend-issueHandleRecordPic'});

const {createMessage, createConfirm} = useMessage();
const selList = ref<any[]>([]); // 选中的隐患记录列表

const [registerTable, {reload, setLoading, getFetchParams}] = useTable({
  api: api.page,
  columns: issuePicColumn,
  useSearchForm: true,
  ellipsis: false,
  immediate: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      // console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      genQueryInput('整改单编号', 'issueNum'),
    ],
  },
  searchInfo: {
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
  // actionColumn: { width: 120, title: '操作', dataIndex: 'action' },
});

function getFirstImageUrl(urlList) {
  console.log('getFirstImageUrl urlList',urlList)
  if (urlList && urlList.length > 0) {
    return fileSaveApi.getFileLocal(urlList[0]);
  }
  return ''; // 返回空字符串或其他默认值
}

function formatDate(timestamp) {
  return new Date(timestamp).toLocaleString();
}


function handleBatchDownload() {
  createConfirm({
    iconType: 'info',
    title: '下载',
    content: '是否确认下载所有选中图片？',
    onOk: () => {
      setLoading(true)
      api.batchDownloadPic(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}
</script>
