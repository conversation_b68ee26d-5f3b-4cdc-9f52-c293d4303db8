<template>
  <div class="fa-table-title">时间统计</div>
  <div class="fa-flex-row">
    <Chart ref="chartRef" :options="options" class="mt-30px" height="600px" width="900px"/>
    <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="600px" width="900px"/>
  </div>
</template>
<script lang="ts" setup>
import {issueHandleRecordApi} from '/@/api';
import {onMounted, ref, watch} from 'vue';
import {Chart} from '/@/components/Chart';

defineOptions({ name: 'timeData' });
const props = defineProps({
  orgId:String,
});

  const chartRef = ref<any>(null); // ref
  const options = ref({
    title: {
      text: '不同年份隐患记录统计',
      textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
        fontStyle: 'normal',
        fontWeight: 'normal',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        //x轴文字的配置
        show: true,
        interval: 0,//使x轴文字显示全
        rotate: -60
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'50',
      },
    ],
  });

  const chartRef2 = ref<any>(null);
  const options2 = ref({
    title: {
      text: '不同月份隐患记录统计',
      textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
        fontStyle: 'normal',
        fontWeight: 'normal',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        //x轴文字的配置
        show: true,
        interval: 0,//使x轴文字显示全
        rotate: -60
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'50',
      },
    ],
  });  

  const TIME_TYPE_ONE = "year"
  const TIME_TYPE_TWO = "month"

  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      initInstance()
    } catch (error) {
      console.error('Error fetching area data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });

  //柱状图参数赋值
  async function setOP(timeType, selectYear, option) {
    const res = await issueHandleRecordApi.getIssueTimeRecord({
      orgId:props.orgId,
      timeType: timeType,
      selectYear: selectYear
    })
    if (!Array.isArray(res.data)) return;

    // 赋值
    let nameArr = res.data.map(item => item.name);
    let countArr = res.data.map(item => ({
      value: item.count,
      name: item.name,
      id: item.id,
    }));

    option.value.xAxis.data = nameArr;
    option.value.series[0].data = countArr;
  }

  function initInstance(){
    // 替换为您的后台API端点，获取数据
    setOP(TIME_TYPE_ONE,"", options)

    setTimeout(() => {
      console.log(chartRef.value.getInstance());
      const instance = chartRef.value.getInstance();
      if (instance) {
        instance.on('click', function (params) {
          const selectYear = params.data.name;
          setOP(TIME_TYPE_TWO, selectYear, options2)
        });
      }
    }, 100);
  }

function resetOp(option){
  option.value.xAxis.data = [];
  option.value.series[0].data = [];
}

watch(props,
  (val) => {
    console.log('props',val)
    resetOp(options)
    resetOp(options2)
    initInstance()
  },
  {immediate: true} // 如果需要立即执行，可以添加这个选项
)
</script>

<style lang="less" scoped>
  .jnpf-content-wrapper-center {
    overflow-y: auto;
  }
</style>
