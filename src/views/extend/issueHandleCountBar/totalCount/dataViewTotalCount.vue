<template>
  <div class="jnpf-content-wrapper" >
    <div class="jnpf-content-wrapper-center bg-white p-10px">
        <div class="fa-flex-column" style="margin-left: 8px;overflow-x:scroll;">
          <div style="position: fixed;top: 120px;z-index: 99;background-color: white;">
            <div class="fa-table-title">隐患统计</div>
            <div class="fa-flex-row" style="width:2400px;height: 50px;margin:10px  0;border-bottom: 1px solid black;">
              <div class="fa-flex-row" style="margin: 8px;width: 900px;">
                <div class="fa-flex-row-center" style="margin-right: 8px;width: 400px;">
                  <text style="width: 50px;letter-spacing:5px;">组织</text>
                  <JnpfOrganizeSelect v-model="orgId" @change="changeOrg"></JnpfOrganizeSelect>
                </div>
                <div class="fa-flex-center" style="margin-right: 8px;width: 300px;">
                  <!--                <text style="width: 50px;letter-spacing:5px;">类别</text>-->
                  <!--                <Select v-model="issueTypeId" style="width:250px;"></Select>-->
                </div>
              </div>
              <div class="fa-flex-row" style="margin: 8px;width: 900px;">
                <div style="margin-right: 8px;width: 100px;">
                  <a-button @click="changeDate(DATE_TYPE.CURRENT_DAY)">本日</a-button>
                </div>
                <div style="margin-right: 8px;width: 100px;">
                  <a-button @click="changeDate(DATE_TYPE.CURRENT_WEEK)">本周</a-button>
                </div>
                <div style="margin-right: 8px;width: 100px;">
                  <a-button @click="changeDate(DATE_TYPE.CURRENT_MONTH)">本月</a-button>
                </div>
                <div style="margin-right: 8px;width: 100px;">
                  <a-button @click="changeDate(DATE_TYPE.CURRENT_YEAR)">本年</a-button>
                </div>
                <div style="margin-right: 8px;width: 350px;">
                  <JnpfDateRange v-model="timeRange" format="YYYY-MM-DD" @change="changeDate">时间范围组件</JnpfDateRange>
                </div>
              </div>
              <div class="fa-flex-row" style="margin: 8px;width: 400px;">
                <!--              <div style="margin-right: 8px;width: 80px;">-->
                <!--                <a-button type="primary" @click="handleQuery">查询</a-button>-->
                <!--              </div>-->
                <div style="margin-right: 8px;width: 80px;">
                  <a-button @click="resetQuery">重置</a-button>
                </div>
              </div>
            </div>
          </div>
          <div class="fa-flex-column" style="margin-top: 110px;">
            <div>
              <timeData :org-id="orgId"></timeData>
              <areaData :time-range="timeRange" :org-id="orgId"></areaData>
              <typeData :time-range="timeRange" :org-id="orgId"></typeData>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {ref, watch} from 'vue';
import JnpfOrganizeSelect from "/@/components/Jnpf/Organize/src/OrganizeSelect.vue";
import areaData from './AreaData.vue'
import timeData from './TimeData.vue'
import typeData from './TypeData.vue'
import {DATE_TYPE} from "/@/enums/zzEnums";
import {dateUtil, endTime, getCurrentMonthFirst, getCurrentMonthLast, getCurrentWeekStartTimeAndEndTime, getCurrentYearFirst, getCurrentYearLast, startTime} from "/@/utils/dateUtil";

defineOptions({name: 'dataViewTotalCount'});

const orgId = ref<string>()
const issueTypeId = ref<string>()
const timeRange = ref<any[]>([])

function handleQuery(){

}

watch(orgId,
  (val) => {
    console.log('orgId', val)
  },
  {immediate: true} // 如果需要立即执行，可以添加这个选项
)

watch(timeRange,
  (val) => {
    console.log('timeRange', val)
  },
  {immediate: true} // 如果需要立即执行，可以添加这个选项
)

function changeOrg(msg) {
  console.log('updateOrg msg', msg)
  if (Array.isArray(msg) && msg.length > 0) {
    orgId.value = msg[msg.length - 1]
  } else {
    orgId.value = undefined
  }
}
function changeDate(obj) {
  console.log('changeDate msg', obj)
  if (obj === DATE_TYPE.CURRENT_DAY) {
    let arr = [
      startTime(new Date()),
      endTime(new Date())
    ]
    timeRange.value = arr
    return
  }

  if (obj === DATE_TYPE.CURRENT_WEEK) {
    let weekTime = getCurrentWeekStartTimeAndEndTime(new Date());
    let arr = [weekTime.startTime, weekTime.endTime]
    timeRange.value = arr
    return
  }

  if (obj === DATE_TYPE.CURRENT_MONTH) {
    let arr = [
      getCurrentMonthFirst(new Date()),
      getCurrentMonthLast(new Date())
    ]
    timeRange.value = arr
    return
  }

  if (obj === DATE_TYPE.CURRENT_YEAR) {
    let arr = [
      getCurrentYearFirst(new Date()),
      getCurrentYearLast(new Date())
    ]
    timeRange.value = arr
    return
  }
  timeRange.value = obj
}

function resetQuery(){
  timeRange.value = []
  orgId.value = undefined
}
</script>

<style lang="less" scoped>
.jnpf-content-wrapper-center {
  overflow-y: auto;
}
</style>
