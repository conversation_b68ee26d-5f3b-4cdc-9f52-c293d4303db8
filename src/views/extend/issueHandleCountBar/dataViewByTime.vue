<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart ref="chartRef" :options="options" class="mt-30px" height="500px" />
      <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { issueAreaApi, issueHandleRecordApi } from '/@/api';
  import { reactive, onMounted, ref } from 'vue';
  import { Chart } from '/@/components/Chart';
  import {ISSUE_TYPE_ENUM} from "/@/enums/issueEnums";

  defineOptions({ name: 'dataViewByTime' });

  const chartRef = ref<any>(null); // ref
  const options = ref({
    title: {
      text: '不同年份隐患记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'100',
      },
    ],
  });

  const chartRef2 = ref<any>(null);
  const options2 = ref({
    title: {
      text: '不同月份隐患记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'100',
      },
    ],
  });  

  const TIME_TYPE_ONE = "year"
  const TIME_TYPE_TWO = "month"


  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      // 替换为您的后台API端点，获取数据
      setOP(TIME_TYPE_ONE,"", options)

      setTimeout(() => {
        console.log(chartRef.value.getInstance());
        const instance = chartRef.value.getInstance();
        if (instance) {
          instance.on('click', function (params) {
            const selectYear = params.data.name;
            setOP(TIME_TYPE_TWO, selectYear, options2)
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error fetching area data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });

  //柱状图参数赋值
  async function setOP(timeType, selectYear, option) {
    const res = await issueHandleRecordApi.getIssueTimeRecord({
      "timeType": timeType,
      "selectYear": selectYear
    })
    if (!Array.isArray(res.data)) return;

    // 赋值
    let nameArr = res.data.map(item => item.name);
    let countArr = res.data.map(item => ({
      value: item.count,
      name: item.name,
      id: item.id,
    }));

    option.value.xAxis.data = nameArr;
    option.value.series[0].data = countArr;
  }

</script>

<style lang="less" scoped>
  .jnpf-content-wrapper-center {
    overflow-y: auto;
  }
</style>
