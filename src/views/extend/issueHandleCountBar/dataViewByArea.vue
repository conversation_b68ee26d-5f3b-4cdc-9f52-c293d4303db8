<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart ref="chartRef" :options="options" class="mt-30px" height="500px" />
      <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="500px" />
      <Chart ref="chartRef3" :options="options3" class="mt-30px new-chart" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {issueAreaApi, issueHandleRecordApi} from '/@/api';
import {onMounted, ref} from 'vue';
import {Chart} from '/@/components/Chart';

defineOptions({ name: 'dataViewByArea' });

  const chartRef = ref<any>(null); // ref
  const options = ref({
    title: {
      text: '各区域分期隐患记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        //x轴文字的配置
        show: true,
        interval: 0,//使x轴文字显示全
      }
    },
    xAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'50',
      },
    ],
  });

  const chartRef2 = ref<any>(null);
  const options2 = ref({
    title: {
      text: '二级区域下隐患记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: [],
    },
    xAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'50',
      },
    ],
  });

const chartRef3 = ref<any>(null);
const options3 = ref({
  title: {
    text: '二级区域下隐患记录分类统计',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center',
    feature: {
      mark: { show: true },
      dataView: { show: true, readOnly: false },
      magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
      restore: { show: true },
      saveAsImage: { show: true },
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  yAxis: {
    type: 'category',
    data: [],
  },
  xAxis: {
    type: 'value',
  },
  series: [
    {
      data: [],
      type: 'bar',
      barWidth:'50',
    },
  ],
});

// 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      setOP('0',options)

      setTimeout(() => {
        // console.log(chartRef.value.getInstance());
        const instance = chartRef.value.getInstance();
        if (instance) {
          instance.on('click', function (params) {
            // console.log('点击了柱状图，数据索引为：', params);
            const areaId = params.data.id;
            setOP(areaId,options2,false)
          });
        }

        const instance2 = chartRef2.value.getInstance();
        if (instance2) {
          instance2.on('click', function (params) {
            // console.log('点击了柱状图，数据索引为：', params);
            const areaId = params.data.id;
            setOP(areaId,options3,true)
          });
        }
      }, 100);
    } catch (error) {
      console.error('Error fetching area data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });

  function getRandomRGB ():string {
    const r = Math.floor(Math.random() * 255);
    const g = Math.floor(Math.random() * 255);
    const b = Math.floor(Math.random() * 255);
    let rgb =`rgb(${r},${g},${b})`
    console.log('rgb',rgb)
    return rgb;
  };

  async function setOP(areaId, option, countType:boolean) {
    console.log('setOP',setOP)

    let res = {}
    if (countType){
      res = await issueHandleRecordApi.getIssueAreaRecordCount({parentId:areaId})
    }else {
      res = await issueAreaApi.getAreaRecord({parentId:areaId})
    }
    if (!res.data || !Array.isArray(res.data)) return;

    // 赋值
    let nameArr = res.data.map(item => item.name);
    let countArr = res.data.map(item => {
      let randomRGB = getRandomRGB()
        return {
          value: item.count,
          name: item.name,
          id: item.id,
          itemStyle: {color: randomRGB}
        }
      }
    );

    console.log('countArr',countArr)
    option.value.yAxis.data = nameArr;
    option.value.series[0].data = countArr;
  }
</script>

<style lang="less" scoped>
  .jnpf-content-wrapper-center {
    overflow-y: auto;
  }
</style>
