<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center bg-white p-10px">
      <Chart ref="chartRef" :options="options" class="mt-30px" height="500px" />
      <Chart ref="chartRef2" :options="options2" class="mt-30px new-chart" height="500px" />
      <Chart ref="chartRef3" :options="options3" class="mt-30px new-chart" height="500px" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { issueAreaApi, issueHandleRecordApi } from '/@/api';
  import { reactive, onMounted, ref } from 'vue';
  import { Chart } from '/@/components/Chart';
  import {ISSUE_TYPE_ENUM} from "/@/enums/issueEnums";

  defineOptions({ name: 'dataViewByType' });

  const chartRef = ref<any>(null); // ref
  const options = ref({
    title: {
      text: '隐患分类一级记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: [],
    },
    xAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'100',
      },
    ],
  });

  const chartRef2 = ref<any>(null);
  const options2 = ref({
    title: {
      text: '隐患分类二级记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: [],
    },
    xAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'100'
      },
    ],
  });  
  
  const chartRef3 = ref<any>(null);
  const options3 = ref({
    title: {
      text: '隐患分类三级记录统计',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
        restore: { show: true },
        saveAsImage: { show: true },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    yAxis: {
      type: 'category',
      data: [],
    },
    xAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'bar',
        barWidth:'100',
      },
    ],
  });

  // 生命周期钩子：组件挂载后调用 API
  onMounted(async () => {
    try {
      // 替换为您的后台API端点，获取数据
      const issueTypeArr = new Array(ISSUE_TYPE_ENUM.ISSUE_TYPE_ONE, ISSUE_TYPE_ENUM.ISSUE_TYPE_THREE);

      //获取后台数据并赋值
      for (const issueType of issueTypeArr) {
        const res = await issueHandleRecordApi.getIssueTypeRecord({
          "issueType": issueType,
          "parentId": ""
        })
        if (!Array.isArray(res.data)) return;

        if (issueType === ISSUE_TYPE_ENUM.ISSUE_TYPE_ONE) {
          setOP(res.data, options)
        } else {
          setOP(res.data, options3)
        }
      }

      setTimeout(() => {
        console.log(chartRef.value.getInstance());
        const instance = chartRef.value.getInstance();
        if (instance) {
          instance.on('click',setOpTwo);
        }
      }, 100);
    } catch (error) {
      console.error('Error fetching area data:', error);
      // 在这里您可以处理错误，比如显示一个错误消息给用户
    }
  });

  async function setOpTwo(params) {
    console.log('点击了柱状图，数据索引为：', params);
    const typeOneId = params.data.id;
    const res = await issueHandleRecordApi.getIssueTypeRecord({
      "issueType": ISSUE_TYPE_ENUM.ISSUE_TYPE_TWO,
      "parentId": typeOneId
    })

    if (!Array.isArray(res.data)) return;
    setOP(res.data, options2)
  }

  const TYPE_COLOR_MAP = {
    "物的不安全状态":"skyblue",
    "人的不安全行为":"red",
    "环境因素":"green",
    "管理缺陷":"purple",
    "安全文明施工":"blue"
  }

  //柱状图参数赋值
  function setOP(records: any[], option) {
    // 赋值
    let nameArr = records.map(item => item.name);
    let countArr = records.map(item => ({
      value: item.count,
      name: item.name,
      id: item.id,
      itemStyle: {color: TYPE_COLOR_MAP[item.name]}
    }));

    option.value.yAxis.data = nameArr;
    option.value.series[0].data = countArr;
  }

</script>

<style lang="less" scoped>
  .jnpf-content-wrapper-center {
    overflow-y: auto;
  }
</style>
