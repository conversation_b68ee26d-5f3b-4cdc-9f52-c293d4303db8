<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
<!--            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新建</a-button>-->
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from '/@/components/Table';
import {useI18n} from '/@/hooks/web/useI18n';
import {blackListApi, userMasterApi} from '/@/api';
import {useMessage} from '/@/hooks/web/useMessage';
import {usePopup} from '/@/components/Popup';
import {genQuerySearch} from '/@/utils/tableUtils';
import {reactive} from "vue";

defineOptions({ name: 'black-list' });

  const { t } = useI18n();
  const { createMessage } = useMessage();
  const columns: BasicColumn[] = [
    // { title: '序号', dataIndex: 'id', width: 80 },
    { title: '账号', dataIndex: 'account', },
    { title: '姓名', dataIndex: 'realName', },
    {
      title: '性别',
      dataIndex: 'gender',
      align: 'center',
      customRender: ({ record }) => {
        const text = record.gender == 1 ? '男' : record.gender == 2 ? '女' : '保密';
        return text;
      },
    },
    { title: '年龄', dataIndex: 'age' },
    { title: '手机', dataIndex: 'mobilePhone' },
    { title: '所属组织', dataIndex: 'organizeName'  },
    { title: '岗位', dataIndex: 'positionName'  },
    { title: '用工形式', dataIndex: 'formEmployment'  },
    {
      title: '三级培训状态',
      dataIndex: 'inEduStatus',
      customRender: ({ record }) => {
        const text = record.inEduStatus == 0 ? '未完成' : record.inEduStatus == 1 ? '已完成' : '/';
        return text;
      },
    },
    { title: '创建时间', dataIndex: 'creatorTime', width: 120, format: 'date|YYYY-MM-DD' },
  ];
  const searchInfo = reactive({
    "fromSource":2, // 试题库id
  })

  const [registerTable, { reload }] = useTable({
    api: userMasterApi.page,
    columns,
    searchInfo,
    useSearchForm: true,
    formConfig: {
      schemas: [
        genQuerySearch(),
      ],
    },
    // actionColumn: {
    //   width: 150,
    //   title: '操作',
    //   dataIndex: 'action',
    // },
  });

  function getTableActions(record: any): ActionItem[] {
    return [
      // {
      //   label: t('common.editText'),
      //   onClick: addOrUpdateHandle.bind(null, record.id),
      // },
      // {
      //   label: t('common.delText'),
      //   color: 'error',
      //   modelConfirm: {
      //     onOk: handleDelete.bind(null, record.id),
      //   },
      // },
    ];
  }

  function handleDelete(id: any) {
    blackListApi.remove(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  function addOrUpdateHandle(id = '') {
    // console.log('id:',id);
    // openFormPopup(true, { id });
  }

</script>
