<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-left" style="width: 250px;">
      <BasicLeftTree
        title="子项类型"
        ref="leftTreeRef"
        :treeData="treeData"
        :loading="treeLoading"
        :fieldNames="{ key: 'id', title: 'name' }"
        @reload="reloadTree"
        @select="handleTreeSelect"
        showToolbar/>
    </div>

    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">新增</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete()" :disabled="selList.length === 0">批量删除</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>批量导出</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>
        </BasicTable>
      </div>
      <Form @register="registerForm" @reload="reload" />
      <ExportModal @register="registerExportModal" />
      <DeliverCardHisList @register="registerDeliverCardHisList" @reload="reload" />
      <DeliverCardFlowHisList @register="registerDeliverCardFlowHisList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref, toRefs, unref} from "vue";
import {BasicLeftTree, TreeActionType} from "/@/components/Tree";
import {ActionItem, BasicColumn, BasicTable, TableAction, useTable} from "/@/components/Table";
import {deliverCardApi, dicDataApi} from "/@/api";
import {useMessage} from "/@/hooks/web/useMessage";
import Form from "./Form.vue";
import {useModal} from "/@/components/Modal";
import {genQuerySearch} from "/@/utils/tableUtils";
import {useDrawer} from "/@/components/Drawer";
import { usePopup } from "/@/components/Popup";
import {getDicTypeByEnCode} from "/@/api/systemData/dictionary";
import {isNil} from "lodash-es";
import ExportModal from "./ExportModal.vue";
import DeliverCardHisList from './DeliverCardHisList.vue';
import DeliverCardFlowHisList from './DeliverCardFlowHisList.vue';

defineOptions({name: 'safeDeliver-deliverCard'});

interface State {
  treeData: any[];
  treeLoading: boolean;
}

const state = reactive<State>({
  treeData: [],
  treeLoading: false,
});
const {treeData, treeLoading} = toRefs(state);
const leftTreeRef = ref<Nullable<TreeActionType>>(null);


async function init() {
  setLoading(true);
  await reloadTree();
  searchInfo.kidItem = state.treeData[0]?.id;
  const leftTree = unref(leftTreeRef);
  leftTree?.setSelectedKeys([searchInfo.kidItem]);
  reload();
}

// ----------------------------------- left tree -----------------------------------
/** 重新加载Tree数据 */
async function reloadTree() {
  state.treeLoading = true;

  /* 获取字典表中子项数据 */
  let dicTypeId;
  await getDicTypeByEnCode('safeDeliver.kidItem').then(_res => {
    dicTypeId = _res.data.id;
  })
  if (isNil(dicTypeId)) return;

  const ret = await dicDataApi.list({
    dictionaryTypeId: dicTypeId,
    parentId: '0'
  });
  ret.data = ret.data.map(i => {
    return {
      name: i.fullName,
      ...i
    }
  })

  state.treeData = [
    {id: '', name: '全部'},
    ...ret.data,
  ];
  state.treeLoading = false
}

/** Tree节点点击 */
async function handleTreeSelect(id: any) {
  console.log('handleTreeSelect', id)
  searchInfo.kidItem = id;
  reload()
}

// ----------------------------------- right table -----------------------------------
const {createMessage, createConfirm} = useMessage();
const [registerForm, {openPopup: openFormModal}] = usePopup();
const [registerExportModal, {openModal: openExportModal}] = useModal();
const [registerDeliverCardHisList, { openDrawer: openDeliverCardHisList }] = useDrawer();
const [registerDeliverCardFlowHisList, { openDrawer: openDeliverCardFlowHisList }] = useDrawer();

const columns: BasicColumn[] = [
  {title: '交底卡名称', dataIndex: 'cardName', width: 250},
  {title: '编号', dataIndex: 'num', width: 250},
  {title: '子项类型', dataIndex: 'kidItemName', width: 250},
  {title: '版本', dataIndex: 'version', width: 70},
  {title: '编制人', dataIndex: 'compilePerName', width: 100},
  //{title: '是否最新版', dataIndex: 'isNewst', width: 250, customRender: ({record}) => (record.isNewst? '是' : '/')},
  {title: '发布时间', dataIndex: 'releaseTime', width: 120, format: 'date|YYYY-MM-DD'},
  {title: '专业', dataIndex: 'subject', width: 100},
  // {title: '最新版', dataIndex: 'newstVersion', width: 100},
  {title: '施工方案名称', dataIndex: 'constructName', width: 250},
  {title: '施工方案编号', dataIndex: 'constructNum', width: 250},
  {title: '施工方案版本', dataIndex: 'constructVersion', width: 250},
  {title: '施工方案级别', dataIndex: 'constructLevel', width: 250},
];
const searchInfo = reactive({
  isNewst: 1, // 只查询最新版
  kidItem: '', // 右table所属左treeId
  _sorter: 'f_creator_time DESC', // 按照创建时间倒序排列
});
const selList = ref<any[]>([]); // 选中的试题列表

const [registerTable, {reload, setLoading, getFetchParams}] = useTable({
  api: deliverCardApi.page,
  columns,
  searchInfo,
  useSearchForm: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      // console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  immediate: false,
  ellipsis:false,
  formConfig: {
    schemas: [
      // genQuerySelect('部门', 'departmentId'),
      genQuerySearch(),
    ],
  },
  actionColumn: {
    width: 320,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record:any): ActionItem[] {
  return [
    {
      label: '编辑',
      onClick: addOrUpdateHandle.bind(null, record.id),
      auth:'btn_edit',
    },
    {
      label: '发布新版',
      onClick: addNewVersion.bind(null, record),
      auth:'btn_edit',
    },
    {
      label: `版本历史(${record.versionCount})`,
      onClick: handleOpenHisList.bind(null, record),
      auth:'btn_edit',
    },
    {
      label: `交底记录(${record.flowCount})`,
      onClick: handleOpenFlowHisList.bind(null, record),
      auth:'btn_edit',
    },
    {
      label: '删除',
      color: 'error',
      auth:'btn_remove',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function addNewVersion(r:any) {
  openFormModal(true, { ...r, _type: 'addNewVersion' });
}

function handleOpenHisList(r:any) {
  openDeliverCardHisList(true, r)
}

function handleOpenFlowHisList(r:any) {
  openDeliverCardFlowHisList(true, r)
}

function handleDelete(id) {
  deliverCardApi.remove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function addOrUpdateHandle(id = '') {
  openFormModal(true, {
    id,
    kidItem: searchInfo.kidItem,
  });
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中交底卡？',
    onOk: () => {
      setLoading(true)
      deliverCardApi.removeBatchByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

function handleExport() {
  const listQuery = {
    ...getFetchParams(),
    condition: getFetchParams().condition || '',
    keyword: getFetchParams().keyword || '',
  };
  openExportModal(true, {listQuery});
}

onMounted(() => init());
</script>


