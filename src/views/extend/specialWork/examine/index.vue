<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'approvalState'">
              <SpecialWorkAuditState :state="record.approvalState"/>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)"/>
            </template>
          </template>

          <template #expandedRowRender="{ record }">
            <BasicTable @register="registerGoodsTable" :data-source="record.goodsList">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'typeAction'">
                  <TableAction :actions="getTypeTableActions(record)"/>
                </template>
              </template>
            </BasicTable>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerExamineFile" @reload="reloadAll"/>
    <SpecialFile @register="registerSpecialFile" @reload="refreshFileList"/>
    <TypeFile @register="registerTypeFile"/>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import { specialWorkApi, specialWorkCertificateApi } from '/@/api';
import { genQueryDept, genQueryInput, genQuerySearch } from '/@/utils/tableUtils';
import { useModal } from '/@/components/Modal';
import Form from './Form.vue';
import { useMessage } from '/@/hooks/web/useMessage';
import SpecialFile from '../compile/SpecialFile.vue';
import TypeFile from './TypeFile.vue';
import SpecialWorkAuditState from "/@/views/extend/specialWork/compile/SpecialWorkAuditState.vue";
import { SPECIAL_WORK_AUDIT_STATE, FILE_INITIAL } from "/@/enums/zzEnums";
import { isNil } from "lodash-es";

defineOptions({name: 'extend-specialWork-examine'});

const {createMessage} = useMessage();
const [registerExamineFile, {openModal: openExamineFile}] = useModal();
const [registerSpecialFile, {openModal: openSpecialFile}] = useModal();
const [registerTypeFile, {openModal: openTypeFile}] = useModal();
const columns: BasicColumn[] = [
  // { title: '序号', dataIndex: 'id', width: 80 },
  {title: '用户名称', dataIndex: 'userName'},
  {title: '用户部门', dataIndex: 'userDeptName'},
  {title: '用户身份证', dataIndex: 'userIdCard'},
  {title: '审核图片上传', dataIndex: 'isExaminePicture', customRender: ({value}) => (value ? '已全部上传' : '未全部上传')},
  {title: '状态', dataIndex: 'approvalState'},
];

const goodsColumns: BasicColumn[] = [
  {title: '证件类型', dataIndex: 'typeName'},
  {title: '审核图片上传', dataIndex: 'isExaminePicture', customRender: ({value}) => (value ? '已上传' : '/')},
  {title: '审核结果', dataIndex: 'auditResults', customRender: ({value}) => (value ? '已签字' : '/')},
];

const [registerTable, {reload}] = useTable({
  api: specialWorkApi.page,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      genQuerySearch(),
      genQueryInput('用户名称', 'userName'),
      genQueryDept('用户部门', 'userDept'),
      genQueryInput('用户身份证', 'userIdCard'),
    ],
  },
  actionColumn: {
    width: 150,
    title: '操作',
    dataIndex: 'action',
  },
  onExpand: handleExpand,
  searchInfo: {approvalState: SPECIAL_WORK_AUDIT_STATE.AUDIT}, // 补充查询参数
});

const [registerGoodsTable] = useTable({
  columns: goodsColumns,
  pagination: false,
  showTableSetting: false,
  canResize: false,
  scroll: {x: undefined},
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'typeAction',
  },
});
const viewRecord = ref()

function handleExpand(expanded:boolean, record:any) {
  if (!expanded || record.goodsList?.length) return;
  refreshChildList(record)
}

async function refreshChildList(record:any) {
  if (isNil(record)) return;
  record.childTableLoading = true;
  await specialWorkCertificateApi.list({specialId: record.id, fileInitial: FILE_INITIAL.HAVE_EDIT}).then(res => {
    record.childTableLoading = false;
    record.goodsList = res.data;
  }).catch(() => (record.childTableLoading = false));
}

function getTableActions(record: any): ActionItem[] {
  return [
    {
      label: '退回',
      color: 'error',
      modelConfirm: {
        content: '您确定要退回这条数据吗, 是否继续?',
        onOk: examineBack.bind(null, record.id),
      },
    },
    // {
    //   label: '上传资料',
    //   onClick: examineFile.bind(null, record.id),
    // },
    {
      label: '提交',
      disabled: !record.isExaminePicture,
      modelConfirm: {
        content: '您确定要提交这条数据吗, 是否继续?',
        onOk: examineSubmit.bind(null, record.id),
      },
    },
  ];
}

function getTypeTableActions(record: any): ActionItem[] {
  return [
    {
      label: '审核图片',
      onClick: uploadFile.bind(null, record),
    },
    {
      label: '查看证件并签字',
      onClick: getTypeFile.bind(null, record),
    },
  ];
}

function getTypeFile(data: any) {
  openTypeFile(true, data);
}

// 查看资料
function examineFile(id) {
  openSpecialFile(true, id);
}

function examineBack(id: any) {
  specialWorkApi.batchBack([id]).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function examineSubmit(id: any) {
  specialWorkApi.batchSubmit([id]).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

// 审核图片
function uploadFile(record) {
  viewRecord.value = record
  openExamineFile(true, {id: record.id});
}

async function refreshFileList() {
  await refreshChildList(viewRecord.value)
}

async function reloadAll() {
  await refreshFileList()
  await reload()
}
</script>
