<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" title="上传人员资料附件" :width="800" :minHeight="400">
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { specialWorkApi, specialWorkTypeApi, specialWorkCertificateApi } from '/@/api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { genCommon, genSelect, genInput, genDate } from '/@/utils/formUtils';
  import { FILE_INITIAL } from '/@/enums/zzEnums';

  const schemas: FormSchema[] = [
    genSelect('上传类型', 'typeId'),
    genCommon('上传证件', 'fileId', 'FaUploadFileQiniu'),
    genInput('相关工种代号', 'jobCodename', false),
    genInput('证件编号', 'certificateNum'),
    genInput('工种工龄(年)', 'jobAge'),
    genDate('初次发证日期', 'firstIssuingDate'),
    genDate('复审日期', 'reviewDate', false),
    genDate('有效期', 'expirationDate', false),
    // genDate('今天日期', 'today'),
    genInput('备注', 'remark', false),
  ];

  const emit = defineEmits(['register', 'reload']);
  const [registerForm, { setFieldsValue, validate, resetFields, updateSchema }] = useForm({ labelWidth: 120, schemas: schemas });
  const [registerModal, { changeLoading, closeModal, changeOkLoading }] = useModalInner(init);
  const { createMessage } = useMessage();

  const specialId = ref();

  async function init(data: { id: string }) {
    console.log(data);
    specialId.value = data;
    await resetFields();
    changeLoading(true);
    // 初始化选择类型
    await getTypeOptions();
    changeLoading(false);
  }

  async function getTypeOptions() {
    const res = await specialWorkTypeApi.list({});
    const options = res.data.map(i => ({
      id: i.id,
      fullName: i.specialWorkType,
    }));
    await updateSchema({ field: 'typeId', componentProps: { options } });
  }

  async function handleSubmit() {
    const values = await validate();
    const params = {
      specialId: specialId.value,
      fileId: values.fileId,
      typeId: values.typeId,
      jobCodename: values.jobCodename,
      certificateNum: values.certificateNum,
      jobAge: values.jobAge,
      firstIssuingDate: values.firstIssuingDate,
      expirationDate: values.expirationDate,
      reviewDate: values.reviewDate,
      // today: values.today,
      remark: values.remark,
    };
    // console.log('-----------', params);
    // changeOkLoading(true);
    // specialWorkApi
    //   .updateSpecialFile(params)
    //   .then(_res => {
    //     // 打印返回数据
    //     createMessage.success('上传附件成功');
    //     changeOkLoading(false);
    //     closeModal();
    //     emit('reload'); // 发布reload事件，外部组件接受此事件
    //   })
    //   .catch(() => changeOkLoading(false));
    // 检查params中的所有值是否都存在
    console.log('-----------', params);
    // if (Object.values(params).every(value => value !== undefined && value !== null)) {
      // console.log('-----------', params);
      changeOkLoading(true);
      specialWorkApi
        .updateSpecialFile(params)
        .then(_res => {
          // 打印返回数据
          createMessage.success('上传附件成功');
          changeOkLoading(false);
          closeModal();
          emit('reload'); // 发布reload事件，外部组件接受此事件
        })
        .catch(() => changeOkLoading(false));
    // } else {
    //   console.error('参数不完整，无法调用specialWorkApi');
    // }
  }

  // Form变化监听方法
  async function handleFieldValueChange(field: any, value: any) {
    // console.log('field', field, 'value', value);
    if ('typeId' === field) {
      // console.log('specialId', specialId.value, 'value', value);
      const certificateData = await specialWorkCertificateApi.list({ typeId: value, specialId: specialId.value, fileInitial: FILE_INITIAL.HAVE_EDIT });
      console.log('certificateData', certificateData.data);
      if (certificateData.data) {
        setFieldsValue({
          fileId: certificateData.data[0].certificateId,
          jobCodename: certificateData.data[0].jobCodename,
          certificateNum: certificateData.data[0].certificateNum,
          jobAge: certificateData.data[0].jobAge,
          firstIssuingDate: certificateData.data[0].firstIssuingDate,
          expirationDate: certificateData.data[0].expirationDate,
          remark: certificateData.data[0].remark,
        });
      }
    }
  }
</script>
