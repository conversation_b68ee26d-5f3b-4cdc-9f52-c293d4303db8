<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
<!--          <template #tableTitle>-->
<!--            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button>-->
<!--          </template>-->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <FaFlowCube ref="flowRef" flow-en-code="issueHandleRecord" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ActionItem, BasicColumn, BasicTable, TableAction, useTable } from '/@/components/Table';
import {issueAreaApi, issueHandleRecordApi as api} from '/@/api';
import {genFlowDetailBtn, genFlowDeleteBtn, genFlowEditBtn, genQueryInput, genQueryDept, genQueryUser, genQueryCascader, genQueryTimeInput} from "/@/utils/tableUtils";
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import { useMessage } from "/@/hooks/web/useMessage";
import { FLOW_OP_TYPE } from "/@/enums/zzEnums";
import { useUserStore } from '/@/store/modules/user';
import {issueColumn} from "/@/enums/issueEnums";
const userStore = useUserStore();
const userInfo = userStore.getUserInfo;

defineOptions({name: 'extend-issueHandleRecord'});

const flowRef = ref<any>();
const { createMessage } = useMessage();
const [registerTable, {reload}] = useTable({
  api: api.page,
  columns:issueColumn,
  useSearchForm: true,
  ellipsis: false,
  formConfig: {
    schemas: [
      genQueryInput('整改单编号', 'issueNum'),
      {
        field:"resAreaTerm",
        label:"责任区域分期",
        component: 'FaCascader',
        componentProps: {placeholder: `输入责任区域分期`, api: issueAreaApi, showRoot: false, submitOnPressEnter: true, maxLevel:1},
      },
      genQueryCascader('责任具体区域', 'resAreaDetail',issueAreaApi),
      genQueryDept('发现部门', 'foundOrg'),
      genQueryDept('责任部门', 'resDep'),
      genQueryUser('发现人','foundPer'),
      genQueryUser('责任人','resPer'),
      {
        field: 'foundDate',
        label: '发现日期',
        component: 'DateRange',
      },
      {
        field: 'handleLimit',
        label: '整改期限',
        component: 'DateRange',
      },
    ],
  },
  searchInfo: {
    //待提交只查询本人和状态属于0的数据
    //已提交，本人，状态不属于0的数据
    //待处理， 状态属于1的数据
    //已处理， 状态值>1 的数据
    //待复查， 状态属于2的数据
    //已复查， 状态值>2 的数据
    'currentStatus#$gt': 0,
    'foundPer':userInfo.userId,
    '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
  },
  actionColumn: { width: 50, title: '操作', dataIndex: 'action' },
});

function getTableActions(record): ActionItem[] {
  return [
    // genFlowEditBtn(record, toDetail),
    // genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
  ];
}

function handleAdd() {
  flowRef.value.handleAdd()
}

function toDetail(record:any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType)
}

function handleDelete(id:string) {
  api.flowRemove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
</script>
