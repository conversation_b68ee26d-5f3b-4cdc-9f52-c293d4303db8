<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增</a-button>
            <a-button type="link" preIcon="icon-ym icon-ym-btn-clearn" @click="handleBatchDelete()" :disabled="selList.length === 0">批量删除</a-button>
            <a-button type="link" @click="handleExport"><i class="icon-ym icon-ym-btn-download button-preIcon"></i>{{ t('common.exportText') }}</a-button>
            <a-button type="link" @click="handleImport"><i class="icon-ym icon-ym-btn-upload button-preIcon"></i>{{ t('common.importText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'currentState'">
              <FaFlowStatus :status="record.currentState" />
            </template>
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <ExportModal @register="registerExportModal" />
    <ImportModal @register="registerImportModal" @reload="reload" />
    <FaFlowCube ref="flowRef" flow-en-code="issueHandleRecord" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import {onMounted, reactive, ref} from "vue";
import {ActionItem, BasicTable, TableAction, useTable} from '/@/components/Table';
import {issueAreaApi, issueHandleRecordApi as api} from '/@/api';
import {genFlowDeleteBtn, genFlowDetailBtn, genFlowEditBtn, genQueryCascader, genQueryDept, genQueryInput, genQueryTimeInput, genQueryUser} from "/@/utils/tableUtils";
import FaFlowCube from "/@/components/Fa/common/src/FaFlowCube.vue";
import {useMessage} from "/@/hooks/web/useMessage";
import {FLOW_OP_TYPE} from "/@/enums/zzEnums";
import {issueColumn} from "/@/enums/issueEnums";
import ExportModal from "./ExportModal.vue";
import ImportModal from "./ImportModal.vue";
import {useModal} from "/@/components/Modal";
import {useI18n} from "/@/hooks/web/useI18n";
import dayjs from "dayjs";
import {DATE_TIME_FORMAT} from "/@/utils/dateUtil";


defineOptions({name: 'extend-issueHandleRecord'});

const flowRef = ref<any>();
const { t } = useI18n();
const {createMessage, createConfirm} = useMessage();
const [registerExportModal, { openModal: openExportModal }] = useModal();
const [registerImportModal, { openModal: openImportModal }] = useModal();

const selList = ref<any[]>([]); // 选中的隐患记录列表

const end = dayjs().add(3, 'day').endOf('day').format(DATE_TIME_FORMAT)

const searchInfo = reactive({
  // 'endReqDate#$min': start,
  // 'foundDate#$max': end,
  '_sorter': 'f_creator_time DESC', // 按照创建时间倒序排列
});

const [registerTable, {reload, setLoading, getFetchParams, getForm}] = useTable({
  api: api.page,
  columns:issueColumn,
  searchInfo,
  useSearchForm: true,
  ellipsis: false,
  immediate: true,
  clickToRowSelect: true, // 点击选中条目
  rowSelection: { // 选中行
    onChange: (selectedRowKeys) => { // 选中行change
      // console.log('selectedRowKeys', selectedRowKeys)
      selList.value = selectedRowKeys;
    },
  },
  formConfig: {
    schemas: [
      genQueryInput('整改单编号', 'issueNum'),
      // genQueryCascader('责任区域分期', 'resAreaTerm',issueAreaApi),
      {
        field:"resAreaTerm",
        label:"责任区域分期",
        component: 'FaCascader',
        componentProps: {placeholder: `输入责任区域分期`, api: issueAreaApi, showRoot: false, submitOnPressEnter: true, maxLevel:1},
      },
      genQueryCascader('责任具体区域', 'resAreaDetail',issueAreaApi),
      genQueryDept('发现部门', 'foundOrg'),
      genQueryDept('责任部门', 'resDep'),
      genQueryUser('发现人','foundPer'),
      genQueryUser('责任人','resPer'),
      // genQueryTimeInput('发现日期', 'foundDate'),
      // genQueryTimeInput('整改期限', 'handleLimit'),
      {
        field: 'foundDate',
        label: '发现日期',
        component: 'DateRange',
      },
      {
        field: 'handleLimit',
        label: '整改期限',
        component: 'DateRange',
      },
    ],
  },

  actionColumn: { width: 120, title: '操作', dataIndex: 'action' },
});

function getTableActions(record): ActionItem[] {
  return [
    genFlowEditBtn(record, toDetail),
    genFlowDeleteBtn(record, handleDelete),
    genFlowDetailBtn(record, toDetail),
  ];
}

async function init() {
  setLoading(true);
  // await updateDepartOp();
  reload();
}

function handleAdd() {
  flowRef.value.handleAdd()
}

function toDetail(record:any, opType: FLOW_OP_TYPE) {
  flowRef.value.toDetail(record, opType)
}

function handleExport() {
  const listQuery = {
    ...getFetchParams(),
  };
  console.log('FetchParams',listQuery)
  openExportModal(true, { listQuery });
}

function handleImport() {
  openImportModal(true, {});
}

function handleDelete(id:string) {
  api.flowRemove(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}

function handleBatchDelete() {
  createConfirm({
    iconType: 'warning',
    title: '删除',
    content: '是否确认删除所有选中隐患记录？',
    onOk: () => {
      setLoading(true)
      api.removeByIds(selList.value).then(_res => {
        setLoading(false);
        reload();
      }).catch(() => setLoading(false));
    },
  });
}

onMounted(() => init());
</script>
