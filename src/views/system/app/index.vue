<template>
  <div class="jnpf-content-wrapper">
    <div class="jnpf-content-wrapper-center">
      <div class="jnpf-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">上传APK</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <UploadApkForm @register="registerForm" @reload="reload" />
  </div>
</template>
<script lang="ts" setup>
import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import { getApkList, delApk } from '/@/api/app/app';
import { useMessage } from '/@/hooks/web/useMessage';
import { usePopup } from '/@/components/Popup';
import UploadApkForm from './UploadApkForm.vue';

defineOptions({ name: 'system-app' });

const { t } = useI18n();
const { createMessage } = useMessage();
const columns: BasicColumn[] = [
  { title: '应用名称', dataIndex: 'name' },
  { title: '短链地址', dataIndex: 'shortCode' },
  { title: '应用包名', dataIndex: 'applicationId' },
  { title: '当前版本号', dataIndex: 'versionCode' },
  { title: '当前版本名称', dataIndex: 'versionName' },
  { title: 'iOS版本号', dataIndex: 'iosVersionCode' },
  { title: 'iOS版本名称', dataIndex: 'iosVersionName' },
  { title: '版本信息', dataIndex: 'remark' },
];
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerTable, { reload }] = useTable({
  api: getApkList,
  columns,
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'keyword',
        label: t('common.keyword'),
        component: 'Input',
        componentProps: {
          placeholder: t('common.enterKeyword'),
          submitOnPressEnter: true,
        },
      },
    ],
  },
  actionColumn: {
    width: 100,
    title: '操作',
    dataIndex: 'action',
  },
});

function getTableActions(record): ActionItem[] {
  return [
    {
      label: t('common.editText'),
      onClick: addOrUpdateHandle.bind(null, record.id),
    },
    {
      label: t('common.delText'),
      color: 'error',
      modelConfirm: {
        onOk: handleDelete.bind(null, record.id),
      },
    },
  ];
}

function handleDelete(id) {
  delApk(id).then(res => {
    createMessage.success(res.msg);
    reload();
  });
}
function addOrUpdateHandle(id = '') {
  openFormPopup(true, { id });
}
</script>
