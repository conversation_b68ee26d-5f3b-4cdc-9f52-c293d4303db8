<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="getTitle" showOkBtn @ok="handleSubmit">
    <a-alert message="请上传apk文件，系统会自动解析apk文件内容。" type="warning" show-icon/>
    <BasicForm @register="registerForm" @field-value-change="handleFieldValueChange" class="!px-10px !mt-10px">
    </BasicForm>
  </BasicPopup>
</template>
<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicPopup, usePopupInner } from '/@/components/Popup';
import { BasicForm, FormSchema, useForm } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { create, update, getApkById, getApkInfo } from "/@/api/app/app";

const id = ref('');

const schemas: FormSchema[] = [
  {
    field: 'fileId',
    label: 'Apk',
    component: 'UploadFileQiniu',
    componentProps: {limit: 1, accept: '.apk', fileSize: 1000, prefix: 'app'},
  },
  {
    field: 'name',
    label: '应用名称',
    component: 'Input',
    componentProps: {},
    rules: [{required: true, trigger: 'blur', message: '应用名称不能为空'}],
  },
  {
    field: 'shortCode',
    label: '短链地址',
    component: 'Input',
    defaultValue: '',
    componentProps: {placeholder: '请输入短链地址'},
  },
  {
    field: 'applicationId',
    label: '应用包名',
    component: 'Input',
    defaultValue: '',
    componentProps: {disabled: true},
  },
  {
    field: 'versionCode',
    label: '当前版本号',
    component: 'Input',
    defaultValue: '',
    componentProps: {},
  },
  {
    field: 'versionName',
    label: '当前版本名称',
    component: 'Input',
    defaultValue: '',
    componentProps: {},
  },
  {
    field: 'iosVersionCode',
    label: 'iOS版本号',
    component: 'Input',
    defaultValue: '',
    componentProps: {},
  },
  {
    field: 'iosVersionName',
    label: 'iOS版本名称',
    component: 'Input',
    defaultValue: '',
    componentProps: {},
  },
  {
    field: 'iconId',
    label: '图标文件',
    component: 'Input',
    defaultValue: '',
    componentProps: {disabled: true},
  },
  {
    field: 'remark',
    label: '版本信息',
    component: 'Textarea',
    defaultValue: '',
    componentProps: {placeholder: '请输入版本信息'},
  },
];
const getTitle = computed(() => (!unref(id) ? '新建App' : '编辑App'));
const emit = defineEmits(['register', 'reload']);
const {createMessage} = useMessage();
const [registerForm, {setFieldsValue, validate, resetFields, updateSchema, clearValidate}] = useForm({labelWidth: 100, schemas: schemas});
const [registerPopup, {closePopup, changeLoading, changeOkLoading}] = usePopupInner(init);

async function init(data) {
  resetFields();
  id.value = data.id;
  setFieldsValue({
    fileId: [],
  });
  if (id.value) {
    changeLoading(true);
    getApkById(id.value).then(res => {
      const data = {
        ...res.data,
        files: res.data.files ? JSON.parse(res.data.files) : [],
        fileId: res.data.fileId ? JSON.parse(res.data.fileId) : [],
      };
      setFieldsValue(data);
      changeLoading(false);
    });
  }
}

function handleFieldValueChange(field, value) {
  console.log('field', field, 'value', value);
  if (field === 'fileId') {
    getApkInfo(value[0]).then(res => {
      createMessage.info('解析APK信息中，请稍等...')
      // console.log('getApkInfo', res)
      if (res.code === 200) {
        setFieldsValue({
          name: res.data.name,
          shortCode: res.data.shortCode,
          applicationId: res.data.applicationId,
          versionCode: res.data.versionCode,
          versionName: res.data.versionName,
          iconId: res.data.iconId,
        })
      }
    })
  }
}

async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: id.value,
    fileId: JSON.stringify(values.fileId),
  };
  const formMethod = id.value ? update : create;
  formMethod(query)
    .then(res => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closePopup();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
