<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" :title="title" class="full-popup">
    <template v-if="groupList.length">
      <a-tabs class="tabs-box" v-model:activeKey="activeKey">
        <a-tab-pane v-for="tab in groupList" :key="tab.id" :tab="tab.fullName"></a-tab-pane>
      </a-tabs>
      <ScrollContainer class="px-20px">
        <template v-for="item in authorizeList">
          <p class="title">{{ item.fullName }}</p>
          <p class="content">
            <template v-if="item.type == 1">
              <span v-for="(child, i) in item.list" :key="i">{{ child.fullName }}</span>
            </template>
            <template v-else-if="item.type == 2">未分配{{ item.fullName }}</template>
            <template v-else>未设置{{ item.fullName }}（无权限控制）</template>
          </p>
        </template>
      </ScrollContainer>
    </template>
    <div class="no-data" v-else>
      <Empty :image="simpleImage" :description="description" />
    </div>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { getPermissionGroup, getPermissionInfo } from '/@/api/system/menu';
  import { BasicPopup, usePopupInner } from '/@/components/Popup';
  import { ScrollContainer } from '/@/components/Container';
  import { reactive, toRefs, watch, ref } from 'vue';
  import { Empty } from 'ant-design-vue';

  interface State {
    title: string;
    activeKey: any;
    id: string;
    groupList: any[];
    authorizeList: any[];
    description: string;
  }

  const state = reactive<State>({
    title: '',
    activeKey: '',
    id: '',
    groupList: [],
    authorizeList: [],
    description: '',
  });
  const { title, activeKey, groupList, authorizeList, description } = toRefs(state);
  const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);
  const [registerPopup, { changeLoading }] = usePopupInner(init);

  watch(
    () => state.activeKey,
    () => initData(),
  );

  function init(data) {
    state.title = data.fullName + '菜单的权限';
    state.id = data.id || '';
    state.activeKey = '';
    state.authorizeList = [];
    state.groupList = [];
    if (state.id) {
      changeLoading(true);
      getPermissionGroup(state.id)
        .then(res => {
          changeLoading(false);
          state.description = res.data?.type == 0 ? '未开启权限' : '该菜单暂未分配权限';
          state.groupList = res?.data?.list || [];
          if (state.groupList.length) state.activeKey = state.groupList[0].id;
        })
        .catch(() => {
          changeLoading(false);
        });
    }
  }
  function initData() {
    if (!state.activeKey || !state.id) return;
    changeLoading(true);
    state.authorizeList = [];
    getPermissionInfo(state.id, state.activeKey)
      .then(res => {
        changeLoading(false);
        for (let [_key, value] of Object.entries(res.data)) {
          state.authorizeList.push(value);
        }
      })
      .catch(() => {
        changeLoading(false);
      });
  }
</script>
<style lang="less" scoped>
  .tabs-box {
    :deep(.ant-tabs-tab:first-child) {
      margin-left: 20px;
    }
  }

  .title {
    font-weight: bold;
    line-height: 36px;
  }

  .content {
    min-height: 20px;
    margin-bottom: 10px;

    span {
      position: relative;
      padding-right: 20px;
    }

    span:not(:last-child) {
      &::after {
        content: '';
        position: absolute;
        top: 3px;
        right: 10px;
        width: 1px;
        height: 15px;
        background: @text-color-label;
        z-index: 1;
      }
    }
  }

  .no-data {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
